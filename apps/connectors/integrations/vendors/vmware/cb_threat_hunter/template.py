from apps.connectors.integrations.template import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import CbThreatHunterV1TemplateVersion


class CbThreatHunterTemplate(Template):
    id = "cb_threat_hunter"
    name = "VMware Carbon Black Enterprise EDR"
    category = Template.Category.ENDPOINT_SECURITY
    versions = {
        CbThreatHunterV1TemplateVersion.id: CbThreatHunterV1TemplateVersion(),
    }
    endpoint_coverage_available = True
    vendor = Vendors.VMWARE
