from pydantic import Field, HttpUrl

from apps.connectors.integrations.template import (
    ConnectionTemplate,
    TemplateVersionConfig,
)
from apps.connectors.integrations.types import EncryptedStr


class CbThreatHunterV1Config(TemplateVersionConfig):
    url: HttpUrl = Field(
        title="API Endpoint",
        description="API Endpoint used to communicate with Carbon Black.",
        default="https://defense-prod05.conferdeploy.net/",
    )
    connector_id: str = Field(
        title="API Connector ID",
        max_length=1024,
    )
    api_key: EncryptedStr = Field(
        title="API Key",
        description="API Key used to authenticate with Carbon Black.",
        max_length=1024,
    )
    org_key: str = Field(
        title="API Org Key",
        max_length=1024,
    )
    super_admin_api_id: str = Field(
        title="API Super Admin ID",
        max_length=1024,
    )
    super_admin_api_secret_key: EncryptedStr = Field(
        title="API Super Admin Secret Key",
        description="API Key used to authenticate with Carbon Black.",
        max_length=1024,
    )


class CbThreatHunterV1Connection(ConnectionTemplate):
    id = "cb_threat_hunter"
    name = "CB Threat Hunter"
    config_model = CbThreatHunterV1Config
