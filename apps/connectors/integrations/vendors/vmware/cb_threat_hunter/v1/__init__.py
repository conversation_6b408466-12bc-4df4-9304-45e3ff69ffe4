from apps.connectors.integrations import TemplateVersion

from .connection import CbThreatHunterV1Config, CbThreatHunterV1Connection
from .integration import CbThreatHunterV1Integration
from .settings import CbThreatHunterV1Settings


class CbThreatHunterV1TemplateVersion(TemplateVersion):
    integration = CbThreatHunterV1Integration
    id = "v1"
    name = "v1"
    config_model = CbThreatHunterV1Config
    connection_model = CbThreatHunterV1Connection
    settings_model = CbThreatHunterV1Settings
