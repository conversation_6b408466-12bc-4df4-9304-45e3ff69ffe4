from cbc_sdk import CBCloudAPI
from cbc_sdk.platform import Device


class CbThreatHunterV1Api:
    """
    Carbon Black Cloud API wrapper
    https://developer.carbonblack.com/reference/carbon-black-cloud/platform-apis/
    """

    def __init__(
        self,
        url=None,
        connector_id=None,
        org_key=None,
        api_key=None,
        super_admin_api_id=None,
        super_admin_api_secret_key=None,
    ):
        self.url = url
        self.connector_id = connector_id
        self.org_key = org_key
        self.api_key = api_key
        self.super_admin_api_id = super_admin_api_id
        self.super_admin_api_secret_key = super_admin_api_secret_key

        self._psc_api = None

    def get_devices(self):
        """
        Get all devices from Carbon Black Cloud
        https://developer.carbonblack.com/reference/carbon-black-cloud/platform/latest/devices-api/
        """
        device: Device
        # noinspection PyTypeChecker
        for device in self.psc_api.select(Device):
            device.refresh()
            yield device.to_json()

    @property
    def psc_api(self) -> CBCloudAPI:
        if not self._psc_api:
            self._psc_api = CBCloudAPI(
                url=self.url,
                org_key=self.org_key,
                token=f"{self.super_admin_api_secret_key}/{self.super_admin_api_id}",
            )
        return self._psc_api
