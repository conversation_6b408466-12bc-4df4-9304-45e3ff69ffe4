from typing import Generator

from apps.connectors.integrations.actions import normalize, normalize_last_seen
from apps.connectors.integrations.actions.host_sync import (
    AssetCriticality,
    Host,
    HostSync,
    HostSyncArgs,
    OwnerAttributes,
    build_fqdn,
)
from apps.connectors.integrations.schemas import HostType, OsAttributes, OsFamily
from apps.connectors.integrations.vendors.vmware.vmware_aria.v1.api import paginate
from apps.connectors.integrations.vendors.vmware.vmware_aria.v1.health_check import (
    ReadDeviceInventory,
)


def normalize_host(host_data: dict):
    os_name = ""
    host_type = HostType.SERVER
    os_family = OsFamily.UNKNOWN
    hostname = host_data.get("hostname")
    domain = "vmware.com"  # Please confirm if this is okay
    fqdns = build_fqdn(hostname, domain)
    ip_addresses = [host_data.get("address")]
    mac_addresses = []
    return Host(
        source_id=host_data.get("id"),
        group_names=[],
        hostname=hostname,
        fqdns=fqdns,
        ip_addresses=ip_addresses,
        mac_addresses=mac_addresses,
        os=OsAttributes(host_type=host_type, family=os_family, name=os_name),
        owners=[OwnerAttributes(email=host_data.get("owner"), name="")],
        aad_id=None,
        criticality=AssetCriticality.UNKNOWN,
        last_seen=normalize_last_seen(host_data.get("updatedAt")),
        source_data=host_data,
    )


class VmwareAriaV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_machines, **kwargs):
            yield from page

    def get_permission_checks(self):
        return [ReadDeviceInventory]
