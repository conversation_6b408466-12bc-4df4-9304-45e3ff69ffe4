from apps.connectors.integrations.template import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import VmwareAriaV1TemplateVersion


class VmwareAriaTemplate(Template):
    id = "vmware_aria"
    name = "VMware Aria Automation"
    category = Template.Category.ASSET_SOURCE
    versions = {
        VmwareAriaV1TemplateVersion.id: VmwareAriaV1TemplateVersion(),
    }
    vendor = Vendors.VMWARE
