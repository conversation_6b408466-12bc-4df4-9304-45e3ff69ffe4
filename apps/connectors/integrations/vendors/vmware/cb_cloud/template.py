from apps.connectors.integrations.template import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import CbCloudV1TemplateVersion


class CbCloudTemplate(Template):
    id = "cb_cloud"
    name = "VMware Carbon Black Cloud Endpoint Standard"
    category = Template.Category.ENDPOINT_SECURITY
    versions = {
        CbCloudV1TemplateVersion.id: CbCloudV1TemplateVersion(),
    }
    endpoint_coverage_available = True
    vendor = Vendors.VMWARE
