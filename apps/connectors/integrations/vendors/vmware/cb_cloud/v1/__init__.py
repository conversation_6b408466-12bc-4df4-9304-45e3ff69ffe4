from apps.connectors.integrations import TemplateVersion

from .connection import CbCloudV1Config, CbCloudV1Connection
from .integration import CbCloudV1Integration
from .settings import CbCloudV1Settings


class CbCloudV1TemplateVersion(TemplateVersion):
    integration = CbCloudV1Integration
    id = "v1"
    name = "v1"
    config_model = CbCloudV1Config
    connection_model = CbCloudV1Connection
    settings_model = CbCloudV1Settings
