from typing import Generator

from apps.connectors.integrations.actions import normalize, normalize_last_seen
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSync,
    HostSyncArgs,
)
from apps.connectors.integrations.schemas import HostType, OsAttributes, OsFamily
from apps.connectors.integrations.schemas.operating_system import OS_FAMILY_NAME


def normalize_host(host_data: dict):
    hostname = (
        host_data.get("name").lower().split("\\")[-1] if host_data.get("name") else ""
    )
    ip_addresses = []
    ip_addresses.append(host_data.get("last_internal_ip_address"))
    ip_addresses.append(host_data.get("last_external_ip_address"))
    organization_name = host_data.get("organization_name")
    group_names = str(host_data.get("ad_group_id", "")) or None
    mac_addresses = host_data.get("mac_address")
    raw_os = host_data.get("os", "") or ""
    os_family, __ = OsFamily.from_string(raw_os)
    os_name = str(host_data.get("os_version", "") or "")

    if not os_name.lower().startswith(raw_os.lower()):
        os_family_name = (
            OS_FAMILY_NAME[os_family]
            if os_family != OsFamily.UNKNOWN
            else raw_os.title()
        )
        os_name = " ".join([os_family_name, os_name])

    host_type = HostType.from_os_name(os_name)

    return Host(
        source_id=str(host_data["id"]),
        group_names=[group_names] if group_names else [],
        hostname=hostname,
        _domain=organization_name,
        ip_addresses=ip_addresses,
        mac_addresses=mac_addresses,
        os=OsAttributes(host_type=host_type, family=os_family, name=os_name),
        last_seen=normalize_last_seen(host_data.get("last_contact_time")),
        source_data=host_data,
    )


class CbCloudV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        yield from api.get_devices()

    def get_permission_checks(self):
        return []
