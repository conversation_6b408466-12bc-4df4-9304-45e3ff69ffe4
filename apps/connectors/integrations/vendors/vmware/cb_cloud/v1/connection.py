from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class CbCloudV1Config(TemplateVersionConfig):
    url: HttpUrl = Field(
        title="API Endpoint",
        description="API Endpoint used to communicate with Carbon Black.",
        default="https://defense-prod05.conferdeploy.net/",
    )
    org_key: str = Field(
        title="API Org Key",
        max_length=1024,
    )
    super_admin_api_id: str = Field(
        title="API Super Admin ID",
        max_length=1024,
    )
    super_admin_api_secret_key: EncryptedStr = Field(
        title="API Super Admin Secret Key",
        description="API Key used to authenticate with Carbon Black.",
        max_length=1024,
    )


class CbCloudV1Connection(ConnectionTemplate):
    id = "cb_cloud"
    name = "Carbon Black"
    config_model = CbCloudV1Config
