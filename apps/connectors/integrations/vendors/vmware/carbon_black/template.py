from apps.connectors.integrations.template import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import CarbonBlackV1TemplateVersion


class CarbonBlackTemplate(Template):
    id = "carbon_black"
    name = "Carbon Black"
    category = Template.Category.ENDPOINT_SECURITY
    versions = {
        CarbonBlackV1TemplateVersion.id: CarbonBlackV1TemplateVersion(),
    }
    endpoint_coverage_available = True
    vendor = Vendors.VMWARE
