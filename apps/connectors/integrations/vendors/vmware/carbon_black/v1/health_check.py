from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckResult,
)
from apps.connectors.integrations.integration import IntegrationError


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            self.integration.invoke("get_info")
            return IntegrationHealthCheckResult.PASSED
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED
