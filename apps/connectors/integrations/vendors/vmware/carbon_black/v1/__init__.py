from apps.connectors.integrations import TemplateVersion

from .connection import CarbonBlackV1Config, CarbonBlackV1Connection
from .integration import CarbonBlackV1Integration
from .settings import CarbonBlackV1Settings


class CarbonBlackV1TemplateVersion(TemplateVersion):
    integration = CarbonBlackV1Integration
    id = "v1"
    name = "v1"
    config_model = CarbonBlackV1Config
    connection_model = CarbonBlackV1Connection
    settings_model = CarbonBlackV1Settings
