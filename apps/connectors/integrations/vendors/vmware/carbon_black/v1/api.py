from cbapi import CbResponseAPI
from cbapi.response import Sensor
from cbapi.response.models import SensorPaginatedQuery


class CarbonBlackAPI:
    """
    Carbon Black Response API wrapper
    https://developer.carbonblack.com/reference/enterprise-response/latest/rest-api/

    Note: The carbon black API has a variety of different endpoint. This class currently
    wraps the Response API, but may be used to wrap other APIs in the future.
    """

    def __init__(self, url=None, token=None):
        self.url = url
        self.token = token
        self._api = None

    def get_sensors(self, query=None, batch_size=100):
        """
        Get all sensors from Carbon Black Response
        https://developer.carbonblack.com/reference/enterprise-response/latest/rest-api/#sensorsendpoints
        """
        sensor: Sensor
        query = SensorPaginatedQuery(Sensor, self.api, query=query)
        query = query.batch_size(batch_size)
        for sensor in query:
            yield sensor.original_document

    def get_info(self):
        """
        Get the health of the Carbon Black API
        """
        return self.api.server_info

    @property
    def api(self):
        if not self._api:
            self._api = CbResponseAPI(url=self.url, token=self.token)
        return self._api
