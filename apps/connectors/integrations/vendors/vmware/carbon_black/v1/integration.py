from cbapi.errors import ConnectionError

from apps.connectors.integrations import Integration

from .actions.host_sync import CarbonBlackV1HostSync
from .api import CarbonBlackAPI
from .health_check import ConnectionHealthCheck


class CarbonBlackV1Integration(Integration):
    api_class = CarbonBlackAPI
    exception_types = (ConnectionError,)
    actions = (CarbonBlackV1HostSync,)
    critical_health_checks = (ConnectionHealthCheck,)
