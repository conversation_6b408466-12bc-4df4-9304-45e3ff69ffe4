from typing import Generator

from apps.connectors.integrations.actions import normalize, normalize_last_seen
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSync,
    HostSyncArgs,
)
from apps.connectors.integrations.schemas import HostType, OsAttributes, OsFamily
from apps.connectors.integrations.vendors.vmware.carbon_black.v1.settings import (
    CarbonBlackV1HostSyncSettings,
)

OS_FAMILY_MAP = {
    1: OsFamily.WINDOWS,
    2: OsFamily.MAC,
    3: OsFamily.LINUX,
}


def normalize_host(host_data: dict):
    group_names = str(host_data.get("group_id")) if host_data.get("group_id") else None
    fqdns = host_data.get("computer_dns_name")
    adapters_string = host_data.get("network_adapters") or ""
    adapters = adapters_string.split("|")
    ip_addresses = []
    mac_addresses = []
    for adapter in adapters:
        parts = adapter.split(",") if adapter else []
        ip = parts[0] if parts else None
        ip_addresses.append(ip)
        mac = parts[1] if len(parts) >= 2 else None
        mac_addresses.append(mac)

    hostname = host_data.get("computer_name", "")
    # TODO: Deal with linux newline in the display string.
    os_name = host_data.get("os_environment_display_string")
    host_type = HostType.from_os_name(os_name)
    os_family, __ = OsFamily.from_string(
        host_data.get("os_type"), os_family_map=OS_FAMILY_MAP
    )

    return Host(
        source_id=str(host_data["id"]),
        group_names=[group_names] if group_names else [],
        hostname=hostname,
        fqdns=[fqdns] if fqdns else [],
        ip_addresses=ip_addresses,
        mac_addresses=mac_addresses,
        os=OsAttributes(host_type=host_type, family=os_family, name=os_name),
        last_seen=normalize_last_seen(host_data.get("last_checkin_time")),
        source_data=host_data,
    )


class CarbonBlackV1HostSync(HostSync):
    settings: CarbonBlackV1HostSyncSettings

    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for sensor in api.get_sensors():
            if not self.settings.fetch_offline_sensors:
                if sensor.get("status") == "Offline":
                    continue

            if not self.settings.fetch_uninstalled_sensors:
                if sensor.get("status") in ("Uninstalled", "Uninstall Pending"):
                    continue

            yield sensor

    def get_permission_checks(self):
        return []
