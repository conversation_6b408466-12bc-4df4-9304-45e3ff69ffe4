from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class CarbonBlackV1Config(TemplateVersionConfig):
    token: EncryptedStr = Field(
        title="Admin Token",
        description="API Key used to authenticate with Carbon Black.",
    )
    url: HttpUrl = Field(
        title="API Endpoint",
        description="API Endpoint used to communicate with Carbon Black.",
    )


class CarbonBlackV1Connection(ConnectionTemplate):
    id = "carbon_black"
    name = "Carbon Black"
    config_model = CarbonBlackV1Config
