from pydantic import ConfigDict, Field

from apps.connectors.integrations import IntegrationActionType, create_settings_model
from apps.connectors.integrations.template import TemplateVersionActionSettings


class CarbonBlackV1HostSyncSettings(TemplateVersionActionSettings):
    model_config = ConfigDict(title="Carbon Black Fetch Sensors Settings")

    fetch_uninstalled_sensors: bool = Field(
        title="Fetch uninstalled sensors",
        description="Select whether to fetch uninstalled sensors.",
        default=False,
    )

    fetch_offline_sensors: bool = Field(
        title="Fetch offline sensors",
        description="Select whether to fetch offline sensors.",
        default=False,
    )


CarbonBlackV1Settings = create_settings_model(
    "CarbonBlackV1Settings",
    {
        IntegrationActionType.HOST_SYNC: CarbonBlackV1HostSyncSettings,
    },
)
