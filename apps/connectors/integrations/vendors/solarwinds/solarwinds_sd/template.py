from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import SolarwindsSdV1TemplateVersion


class SolarwindsSdTemplate(Template):
    id = "solarwinds_sd"
    name = "SolarWinds Service Desk"
    category = Template.Category.ASSET_SOURCE
    versions = {
        SolarwindsSdV1TemplateVersion.id: SolarwindsSdV1TemplateVersion(),
    }
    vendor = Vendors.SOLARWINDS
