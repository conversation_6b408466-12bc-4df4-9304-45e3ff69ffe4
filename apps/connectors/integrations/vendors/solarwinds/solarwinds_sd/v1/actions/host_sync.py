from typing import Generator

from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSync,
    HostSyncArgs,
    OwnerAttributes,
)
from apps.connectors.integrations.actions.utils import normalize, normalize_last_seen
from apps.connectors.integrations.vendors.solarwinds.solarwinds_sd.v1.api import (
    paginate,
)
from apps.connectors.integrations.vendors.solarwinds.solarwinds_sd.v1.health_check import (
    ReadAllHosts,
)


def get_dates(host_data: dict):
    bioses = host_data.get("hardware", {}).get("bioses", [])
    dates = []
    for bios in bioses:
        dates.append(bios.get("reported_at"))
    return dates


def normalize_host(host_data: dict):
    hostname = host_data.get("hardware").get("name")
    os_name = host_data.get("hardware").get("operating_system")
    mac_addresses = []
    ip_addresses = []
    ip_addresses.append(host_data.get("hardware").get("ip"))
    ip_addresses.append(host_data.get("hardware").get("external_ip"))
    domain_name = host_data.get("hardware").get("domain")
    owner = host_data.get("hardware").get("owner", {})
    owners = (
        [OwnerAttributes(name=owner.get("name", ""), email=owner.get("email"))]
        if any(k in owner for k in ("name", "email"))
        else []
    )

    return Host(
        source_id=host_data.get("hardware").get("id"),
        hostname=hostname,
        _domain=domain_name,
        ip_addresses=ip_addresses,
        mac_addresses=mac_addresses,
        _os_name=os_name,
        owners=owners,
        last_seen=normalize_last_seen(get_dates(host_data)),
        source_data=host_data,
    )


class SolarwindsSdV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_hardwares, **kwargs):
            yield from page

    def get_permission_checks(self):
        return [ReadAllHosts]
