from apps.connectors.integrations.actions.host import (
    RemoveHostFromWatchlist,
    RemoveHostFromWatchlistResult,
)
from apps.connectors.integrations.schemas import HostIdentifierArgs, Message
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.api import (
    CrowdstrikeFalconIpV1Api,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.health_check import (
    ReadIdentityProtectionEntities,
    WriteIdentityProtectionEntities,
)


class CrowdstrikeFalconIpRemoveHostFromWatchlist(RemoveHostFromWatchlist):
    """
    Remove a host from the watchlist in Crowdstrike Falcon Identity Protection.

    This action uses the GraphQL API to remove a host entity from the watchlist.
    """

    def execute(
        self, args: HostIdentifierArgs, **kwargs
    ) -> RemoveHostFromWatchlistResult:
        api: CrowdstrikeFalconIpV1Api = self.integration.get_api()

        # Extract the host ID from the args
        host_id = args.host.value

        # Construct the GraphQL mutation with variables
        mutation = """
        mutation RemoveHostFromWatchlist($entityId: String!) {
            removeEntitiesFromWatchList(input: {
                entityQuery: {
                    entityIds: $entityId
                }
            }) {
                updatedEntities {
                    primaryDisplayName
                    secondaryDisplayName
                    watched
                }
                failures {
                    entityIds
                    errorDetails {
                        message
                    }
                }
            }
        }
        """

        # Execute the mutation with variables
        variables = {"entityId": host_id}
        api.execute_graphql_query(mutation, variables)

        return RemoveHostFromWatchlistResult(
            result=Message(message=f"Host {host_id} removed from watchlist.")
        )

    def get_permission_checks(self, *args, **kwargs):  # pragma: no cover
        return [ReadIdentityProtectionEntities, WriteIdentityProtectionEntities]
