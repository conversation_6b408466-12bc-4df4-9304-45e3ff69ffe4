from apps.connectors.integrations.actions.host import (
    AddHostToWatchlist,
    AddHostToWatchlistResult,
)
from apps.connectors.integrations.schemas import HostIdentifierArgs, Message
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.api import (
    CrowdstrikeFalconIpV1Api,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.health_check import (
    ReadIdentityProtectionEntities,
    WriteIdentityProtectionEntities,
)


class CrowdstrikeFalconIpAddHostToWatchlist(AddHostToWatchlist):
    """
    Add a host to the watchlist in Crowdstrike Falcon Identity Protection.

    This action uses the GraphQL API to add a host entity to the watchlist.
    """

    def execute(self, args: HostIdentifierArgs, **kwargs) -> AddHostToWatchlistResult:
        api: CrowdstrikeFalconIpV1Api = self.integration.get_api()

        # Extract the host ID from the args
        host_id = args.host.value

        # Construct the GraphQL mutation with variables
        mutation = """
        mutation AddHostToWatchlist($entityId: String!) {
            addEntitiesToWatchList(input: {
                entityQuery: {
                    entityIds: $entityId
                }
            }) {
                updatedEntities {
                    primaryDisplayName
                    secondaryDisplayName
                    watched
                }
                failures {
                    entityIds
                    errorDetails {
                        message
                    }
                }
            }
        }
        """

        # Execute the mutation with variables
        variables = {"entityId": host_id}
        api.execute_graphql_query(mutation, variables)

        # Return a Message in the result as required
        return AddHostToWatchlistResult(
            result=Message(message=f"Host {host_id} added to watchlist.")
        )

    def get_permission_checks(self, *args, **kwargs):  # pragma: no cover
        return [ReadIdentityProtectionEntities, WriteIdentityProtectionEntities]
