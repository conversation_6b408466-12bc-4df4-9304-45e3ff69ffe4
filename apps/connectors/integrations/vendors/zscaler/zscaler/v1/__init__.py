from apps.connectors.integrations import TemplateVersion

from .connection import ZscalerV1Config, ZscalerV1Connection
from .integration import ZscalerV1Integration
from .settings import ZscalerV1Settings


class ZscalerV1TemplateVersion(TemplateVersion):
    integration = ZscalerV1Integration
    id = "v1"
    name = "v1"
    config_model = ZscalerV1Config
    connection_model = ZscalerV1Connection
    settings_model = ZscalerV1Settings
