from apps.connectors.integrations import ApiBase


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    yield response
    page = 1
    while len(response) > 0:
        page = page + 1
        response = bound_method(**kwargs, page_number=page)
        if len(response) > 0:
            yield response


class ZscalerV1Api(ApiBase):
    def __init__(self, zscaler_cloud_name, api_key, secret_key):
        self.base_url = f"https://zsapi.{zscaler_cloud_name}/papi/"
        self.zscaler_cloud_name = zscaler_cloud_name
        self.api_key = api_key
        self.secret_key = secret_key
        self.jwt_token = None
        super().__init__(
            base_url=self.base_url, static_headers={"Accept": "application/json"}
        )

    def get_session(self):
        if self.jwt_token:
            return self.session
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
        }
        data = {"apiKey": self.api_key, "secretKey": self.secret_key}

        response = self.session.post(
            self.url("auth/v1/login"), headers=headers, data=data
        )
        self.jwt_token = response.json()["jwtToken"]
        headers["auth-token"] = self.jwt_token
        self.session.headers.update(headers)
        return self.session

    # https://help.zscaler.com/client-connector/public-api-controller#/papi/public/v1/getDevices-get
    def get_devices(self, page_size=5000, page_number=1):
        response = self.get_session().get(
            self.url(f"public/v1/getDevices?pageSize={page_size}&page={page_number}")
        )
        return response.json()
