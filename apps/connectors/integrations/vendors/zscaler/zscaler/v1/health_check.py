from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
)
from apps.connectors.integrations.integration import IntegrationError


class ReadDeviceInventory(IntegrationPermissionsHealthCheck):
    name = "Read devices inventory"
    description = "Read devices inventory from Zscaler"
    value = "devices_inventory:view"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            self.integration.invoke("get_devices", page_size=1)
            return IntegrationHealthCheckResult.PASSED
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        api = self.integration.get_api()
        response = api.get_devices(page_size=1)
        if len(response) > 0:
            return IntegrationHealthCheckResult.PASSED
        else:
            return IntegrationHealthCheckResult.FAILED
