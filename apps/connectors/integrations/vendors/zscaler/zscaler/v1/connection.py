from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class ZscalerV1Config(TemplateVersionConfig):
    # https://help.zscaler.com/client-connector/getting-started-client-connector-api
    zscaler_cloud_name: str = Field(
        title=" Zscaler cloud name",
        description=" Zscaler cloud name used to communicate with Zscaler",
    )
    # https://help.zscaler.com/zscaler-client-connector/about-api-key-management
    api_key: str = Field(
        title="Cliend ID",
        description="Client ID for the Zscaler Client Connector",
    )
    secret_key: EncryptedStr = Field(
        title="Client Secret",
        description="Client secret for the Zscaler Client Connector",
    )


class ZscalerV1Connection(ConnectionTemplate):
    id = "zscaler"
    name = "Zscaler"
    config_model = ZscalerV1Config
