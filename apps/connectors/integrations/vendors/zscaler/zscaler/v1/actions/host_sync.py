from typing import Generator

from apps.connectors.integrations.actions import (
    HostSync,
    normalize,
    normalize_last_seen,
    to_list,
)
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSyncArgs,
    OwnerAttributes,
)
from apps.connectors.integrations.vendors.zscaler.zscaler.v1.api import paginate
from apps.connectors.integrations.vendors.zscaler.zscaler.v1.health_check import (
    ReadDeviceInventory,
)

OS = {
    "1": "iOS",
    "2": "Android",
    "3": "Windows",
    "4": "macOS",
    "5": "Linux",
    "6": "Other",
}


def normalize_host(host_data: dict):
    os_name = OS.get(host_data.get("type"))
    mac_addresses = to_list(host_data.get("macAddress", {}))
    fqdns = host_data.get("machineHostname", {}) or ""

    return Host(
        source_id=host_data["udid"],
        fqdns=fqdns,
        mac_addresses=mac_addresses,
        _os_name=os_name,
        owners=[OwnerAttributes(name=host_data.get("owner"), email=None)],
        last_seen=normalize_last_seen(host_data.get("last_seen_time")),
        source_data=host_data,
    )


class ZscalerV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_devices, **kwargs):
            yield from page

    def get_permission_checks(self):
        return [ReadDeviceInventory]
