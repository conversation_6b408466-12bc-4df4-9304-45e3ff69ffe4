from apps.connectors.integrations import TemplateVersion

from .connection import ZscalerInternetAccessV1Config, ZscalerInternetAccessV1Connection
from .integration import ZscalerInternetAccessV1Integration
from .settings import ZscalerInternetAccessV1Settings


class ZscalerInternetAccessV1TemplateVersion(TemplateVersion):
    integration = ZscalerInternetAccessV1Integration
    id = "v1"
    name = "v1"
    config_model = ZscalerInternetAccessV1Config
    connection_model = ZscalerInternetAccessV1Connection
    settings_model = ZscalerInternetAccessV1Settings
