from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import ZscalerInternetAccessV1TemplateVersion


class ZscalerInternetAccessTemplate(Template):
    id = "zscaler_internet_access"
    name = "Zscaler Internet Access"
    category = Template.Category.NETWORK_SECURITY
    versions = {
        ZscalerInternetAccessV1TemplateVersion.id: ZscalerInternetAccessV1TemplateVersion(),
    }
    vendor = Vendors.ZSCALER
