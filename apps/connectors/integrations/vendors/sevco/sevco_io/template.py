from apps.connectors.integrations.template import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import SevcoIoV1TemplateVersion


class SevcoIoTemplate(Template):
    id = "sevco_io"
    name = "Sevco Asset Management"
    category = Template.Category.ASSET_SOURCE
    versions = {
        SevcoIoV1TemplateVersion.id: SevcoIoV1TemplateVersion(),
    }
    vendor = Vendors.SEVCO
