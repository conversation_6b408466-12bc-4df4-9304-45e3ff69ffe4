from apps.connectors.integrations import ApiBase


def paginate(bound_method, **kwargs):
    page = 0
    items = bound_method(page=page, **kwargs)["items"]
    yield items

    while len(items) > 0:
        page = page + 1
        items = bound_method(page=page, **kwargs)["items"]
        yield items


class SevcoIoV1Api(ApiBase):
    def __init__(self, api_key=None, organization_id=None):
        self.base_url = "https://api.sev.co/"
        self.api_key = api_key
        self.organization_id = organization_id
        static_headers = {
            "authorization": "Bearer " + api_key,
            "x-sevco-target-org": organization_id,
            "Content-Type": "application/json",
        }
        super().__init__(static_headers=static_headers, base_url=self.base_url)

    # https://docs.sev.co/reference/getassetlistv3
    def get_unified_devices(self, page=0, per_page=1000):
        data = {"pagination": {"page": page, "per_page": per_page}, "query": {}}
        response = self.session.post(self.url("v3/asset/device"), json=data)
        return response.json()

    # https://docs.sev.co/reference/getorglist
    def get_organizations(self):
        response = self.session.get(self.url("v1/admin/org"))
        return response.json()
