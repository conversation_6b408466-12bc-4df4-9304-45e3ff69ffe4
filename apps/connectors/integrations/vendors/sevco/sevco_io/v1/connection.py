from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class SevcoIoV1Config(TemplateVersionConfig):
    # Do not know if we need to hard code the URL in all examples it is https://api.sev.co/v1/admin/org
    # https://docs.sev.co/docs/using-the-api#creating-an-api-key
    # https://docs.sev.co/docs/using-the-api
    api_key: EncryptedStr = Field(
        title="API Key",
        description="API Key for Sevco.io",
    )
    organization_id: str = Field(
        title="Organization ID",
        description="Organization ID for Sevco.io",
    )


class SevcoIoV1Connection(ConnectionTemplate):
    id = "sevco_io"
    name = "Sevco.io"
    config_model = SevcoIoV1Config
