from apps.connectors.integrations import TemplateVersion

from .connection import SevcoIoV1Config, SevcoIoV1Connection
from .integration import SevcoIoV1Integration
from .settings import SevcoIoV1Settings


class SevcoIoV1TemplateVersion(TemplateVersion):
    integration = SevcoIoV1Integration
    id = "v1"
    name = "v1"
    config_model = SevcoIoV1Config
    connection_model = SevcoIoV1Connection
    settings_model = SevcoIoV1Settings
