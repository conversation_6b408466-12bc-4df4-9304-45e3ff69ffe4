from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class FortimanagerV1Config(TemplateVersionConfig):
    host: str = Field(
        title="Host",
        description="The FortiManager host.",
        max_length=1024,
    )
    username: str = Field(
        title="Username",
        description="The FortiManager username.",
        max_length=1024,
    )
    password: EncryptedStr = Field(
        title="Password",
        description="The FortiManager password.",
        max_length=1024,
    )


class FortimanagerV1Connection(ConnectionTemplate):
    id = "fortimanager"
    name = "FortiManager"
    config_model = FortimanagerV1Config
