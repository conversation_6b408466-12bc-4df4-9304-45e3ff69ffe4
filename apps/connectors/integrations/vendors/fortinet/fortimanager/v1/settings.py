from pydantic import ConfigDict, Field

from apps.connectors.integrations import create_settings_model
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.template import TemplateVersionActionSettings


class FortimanagerV1IPAdressSettings(TemplateVersionActionSettings):
    model_config = ConfigDict(title="Fortimanager Address Group Settings")

    address_group_name: str = Field(
        title="Address Group Name",
        description="The name of the Address Group for blocking IP addresses.",
    )
    adom: str = Field(
        default="root",
        title="ADOM",
        description="The ADOM to use for the API calls.",
    )


FortimanagerV1Settings = create_settings_model(
    "FortimanagerV1Settings",
    {
        IntegrationActionType.BLOCK_IP_FIREWALL: FortimanagerV1IPAdressSettings,
        IntegrationActionType.UNBLOCK_IP_FIREWALL: FortimanagerV1IPAdressSettings,
    },
)
