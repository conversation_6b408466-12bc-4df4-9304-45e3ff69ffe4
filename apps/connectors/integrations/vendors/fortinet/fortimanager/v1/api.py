from apps.connectors.integrations import ApiBase


class FortimanagerV1Api(ApiBase):
    def __init__(self, host=None, username=None, password=None, **kwargs):
        self.base_url = f"https://{host}/jsonrpc"
        self.host = host
        self.username = username
        self.password = password
        self._token = None
        super().__init__(
            base_url=self.base_url, static_headers={"Accept": "application/json"}
        )

    @property
    def token(self):
        """
        Get a session token from the FortiManager API.
        This method sends a request to the FortiManager API to obtain a session token.
        """
        if self._token:
            return self._token
        else:
            payload = {
                "method": "exec",
                "params": [
                    {
                        "url": "/sys/login/user",
                        "data": {
                            "user": self.username,
                            "passwd": self.password,
                        },
                    }
                ],
                "id": 1,
                "session": None,
            }
            response = self.session.post(url=self.base_url, json=payload)
            self._token = response.json().get("session")
            return self._token

    def create_firewall_address(self, address_ip, adom):
        payload = {
            "method": "set",
            "params": [
                {
                    "data": [
                        {
                            "name": f"{address_ip}",
                            "type": "ipmask",
                            "subnet": f"{address_ip}/32",
                        }
                    ],
                    "url": f"/pm/config/adom/{adom}/obj/firewall/address",
                }
            ],
            "session": self.token,
            "id": 1,
        }
        response = self.session.post(self.base_url, json=payload)
        return response.json()

    def add_firewall_address_to_address_group(self, address_group_name, member, adom):
        payload = {
            "method": "add",
            "params": [
                {
                    "data": [member],
                    "url": f"/pm/config/adom/{adom}/obj/firewall/addrgrp/{address_group_name}/member",
                }
            ],
            "session": self.token,
            "id": 1,
        }
        response = self.session.post(self.base_url, json=payload)
        return response.json()

    def delete_firewall_address_from_address_group(
        self, address_group_name, member, adom
    ):
        payload = {
            "method": "delete",
            "params": [
                {
                    "data": [member],
                    "url": f"/pm/config/adom/{adom}/obj/firewall/addrgrp/{address_group_name}/member",
                }
            ],
            "session": self.token,
            "id": 1,
        }
        response = self.session.post(self.base_url, json=payload)
        return response.json()

    def install_object(self, address_group_name, adom):
        payload = {
            "method": "exec",
            "params": [
                {
                    "data": {
                        "adom": f"{adom}",
                        "objects": [
                            [
                                "update",
                                f"obj/firewall/address/{address_group_name}",
                                "",
                                "",
                            ]
                        ],
                        "flags": 0,
                    },
                    "url": "securityconsole/install/objects/v2",
                }
            ],
            "session": self.token,
        }
        response = self.session.post(self.base_url, json=payload)
        return response.json()

    def list_firewall_address(self):
        payload = {
            "method": "get",
            "params": [
                {
                    "url": f"/pm/config/adom/root/obj/firewall/address",
                }
            ],
            "session": self.token,
            "id": 1,
        }
        response = self.session.post(self.base_url, json=payload)
        return response.json()
