from apps.connectors.integrations import ApiBase


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    yield response["items"]
    while response.get("nextLink"):
        next_link = response["nextLink"]
        response = bound_method(**kwargs, next_link=next_link)
        yield response["items"]


class TmVisionOneV1Api(ApiBase):
    def __init__(self, token=None, url=None, **kwargs):
        self.token = token
        static_headers = {"Authorization": "Bearer " + token}
        super().__init__(static_headers=static_headers, base_url=url)

    def get_endpoints(self, top=1000, select=None, next_link=None):
        # https://automation.trendmicro.com/xdr/api-v3#tag/Endpoint-Security/paths/~1v3.0~1endpointSecurity~1endpoints/get
        # top is an enum
        assert top in (10, 50, 100, 200, 500, 1000)
        query_params = {"top": top}
        if select:
            query_params["select"] = select

        if next_link:
            url = next_link
        else:
            url = self.url("/v3.0/endpointSecurity/endpoints")
        response = self.session.get(url, params=query_params)
        return response.json()

    def check_connectivity(self):
        url = self.url("/v3.0/healthcheck/connectivity")
        response = self.session.get(url)
        return response.json()

    def get_detail_endpoint_list(self, ids=None, top=200):
        # https://automation.trendmicro.com/xdr/api-v3#tag/Search/paths/~1v3.0~1eiqs~1endpoints/get
        url = self.url(f"/v3.0/eiqs/endpoints")

        query = ""
        if ids:
            # The request works somewhere between 100 and 200 ids. Set the max to 100 as a round number.
            assert len(ids) <= 100
            query = " or ".join([f"agentGuid eq '{id}'" for id in ids])

        assert top in (50, 100, 200)  # based on the documentation
        query_params = {"top": top}
        headers = {"TMV1-Query": query} if query else {}
        response = self.session.get(url, params=query_params, headers=headers)
        return response.json()
