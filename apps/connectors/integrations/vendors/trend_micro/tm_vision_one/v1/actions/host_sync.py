from typing import Generator

from ata_common.chunking import chunks

from apps.connectors.integrations.actions import normalize, normalize_last_seen
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSync,
    HostSyncArgs,
    to_list,
)
from apps.connectors.integrations.vendors.trend_micro.tm_vision_one.v1.api import (
    paginate,
)
from apps.connectors.integrations.vendors.trend_micro.tm_vision_one.v1.health_check import (
    ReadEndpointInventory,
)


def normalize_host(host_data: dict):
    os_name = host_data.get("osName")
    ip_addresses = to_list(host_data.get("ip", {}).get("value"))
    mac_addresses = to_list(host_data.get("macAddress", {}).get("value"))
    group_names = to_list(
        host_data.get("edrSensor", {}).get("endpointGroup")
        or host_data.get("eppAgent", {}).get("endpointGroup")
    )
    fqdns = host_data.get("endpointName", {}).get("value") or ""

    return Host(
        source_id=host_data["agentGuid"],
        group_names=group_names,
        fqdns=fqdns,
        ip_addresses=ip_addresses,
        mac_addresses=mac_addresses,
        _os_name=os_name,
        last_seen=normalize_last_seen(
            [
                host_data.get("edrSensor", {}).get("lastConnectedDateTime")
                or host_data.get("eppAgent", {}).get("lastConnectedDateTime")
            ]
        ),
        source_data=host_data,
    )


class TmVisionOneV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_endpoints, select="agentGuid", **kwargs):
            items = [item["agentGuid"] for item in page]
            # We are getting bad request using 200 ids per request. Some ids
            # are standard guids and some are larger than 36 characters. I found
            # that we can use 96 large ids per request. I will use 75 to be safe.
            for chunk in chunks(items, 75):
                result_detail = api.get_detail_endpoint_list(ids=chunk, top=200)
                yield from result_detail["items"]

    def get_permission_checks(self):
        return [ReadEndpointInventory]
