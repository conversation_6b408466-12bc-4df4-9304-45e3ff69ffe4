from enum import StrEnum

from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


# enum class with url choices
class TmVisionOneUrls(StrEnum):
    AUSTRALIA = "https://api.au.xdr.trendmicro.com"
    EUROPEAN_UNION = "https://api.eu.xdr.trendmicro.com"
    INDIA = "https://api.in.xdr.trendmicro.com"
    JAPAN = "https://api.xdr.trendmicro.co.jp"
    SINGAPORE = "https://api.sg.xdr.trendmicro.com"
    UNITED_ARAB_EMIRATES = "https://api.mea.xdr.trendmicro.com"
    UNITED_STATES = "https://api.xdr.trendmicro.com"
    UNITES_STATES_GOV = "https://api.usgov.xdr.trendmicro.com"


class TmVisionOneV1Config(TemplateVersionConfig):
    # https://api.xdr.trendmicro.com
    url: TmVisionOneUrls = Field(
        title="API Endpoint",
        description="API Endpoint used to communicate with Trend Micro",
    )
    token: EncryptedStr = Field(
        title="Bearer Token",
        description="API Key used to authenticate with Trend Micro",
    )


class TmVisionOneV1Connection(ConnectionTemplate):
    id = "tm_vision_one"
    name = "Trend Micro Vision One"
    config_model = TmVisionOneV1Config
