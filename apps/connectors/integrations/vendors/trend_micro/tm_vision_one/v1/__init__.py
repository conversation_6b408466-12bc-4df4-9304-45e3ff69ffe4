from apps.connectors.integrations import TemplateVersion

from .connection import TmVisionOneV1Config, TmVisionOneV1Connection
from .integration import TmVisionOneV1Integration
from .settings import TmVisionOneV1Settings


class TmVisionOneV1TemplateVersion(TemplateVersion):
    integration = TmVisionOneV1Integration
    id = "v1"
    name = "v1"
    config_model = TmVisionOneV1Config
    connection_model = TmVisionOneV1Connection
    settings_model = TmVisionOneV1Settings
