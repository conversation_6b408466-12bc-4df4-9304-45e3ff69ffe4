from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
)
from apps.connectors.integrations.integration import IntegrationError


class ReadEndpointInventory(IntegrationPermissionsHealthCheck):
    name = "Read endpoint inventory"
    description = "Read endpoint inventory from Trend Micro Vision One"
    value = "endpoint_inventory:view"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            self.integration.invoke("get_endpoints", top=10, select="agentGuid")
            return IntegrationHealthCheckResult.PASSED
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        api = self.integration.get_api()
        response = api.check_connectivity()
        if "status" in response:
            if response["status"] == "available":
                return IntegrationHealthCheckResult.PASSED
            else:
                return IntegrationHealthCheckResult.FAILED
        else:
            return IntegrationHealthCheckResult.FAILED
