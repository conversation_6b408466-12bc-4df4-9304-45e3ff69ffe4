from apps.connectors.integrations import TemplateVersion

from .connection import UbiquitiUnifiV1Config, UbiquitiUnifiV1Connection
from .integration import UbiquitiUnifiV1Integration
from .settings import UbiquitiUnifiV1Settings


class UbiquitiUnifiV1TemplateVersion(TemplateVersion):
    integration = UbiquitiUnifiV1Integration
    id = "v1"
    name = "v1"
    config_model = UbiquitiUnifiV1Config
    connection_model = UbiquitiUnifiV1Connection
    settings_model = UbiquitiUnifiV1Settings
