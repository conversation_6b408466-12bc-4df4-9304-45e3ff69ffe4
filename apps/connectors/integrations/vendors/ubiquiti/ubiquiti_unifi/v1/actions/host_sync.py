from typing import Generator

from apps.connectors.integrations.actions import (
    HostSync,
    normalize,
)
from apps.connectors.integrations.actions.host_sync import Host, HostSyncArgs
from apps.connectors.integrations.vendors.ubiquiti.ubiquiti_unifi.v1.health_check import (
    ReadDeviceInventory,
)


def normalize_host(host_data: dict):
    fqdns = host_data.get("hostname", {}) or ""
    owners = []
    ip_addresses = [host_data.get("config_network", {}).get("ip", "")]
    mac_addresses = host_data.get("mac", "")
    return Host(
        source_id=host_data.get("_id"),
        fqdns=fqdns,
        ip_addresses=ip_addresses,
        mac_addresses=mac_addresses,
        owners=owners,
        last_seen=None,
        source_data=host_data,
    )


class UbiquitiUnifiV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        page = api.list_devices()
        yield from page

    def get_permission_checks(self):
        return [ReadDeviceInventory]
