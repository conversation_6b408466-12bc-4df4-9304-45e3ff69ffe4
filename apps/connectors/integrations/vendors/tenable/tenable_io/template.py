from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import TenableIoV1TemplateVersion


class TenableIoTemplate(Template):
    id = "tenable_io"
    name = "Tenable Vulnerability Management"
    category = Template.Category.VULNERABILITY_MANAGEMENT
    versions = {
        TenableIoV1TemplateVersion.id: TenableIoV1TemplateVersion(),
    }
    vendor = Vendors.TENABLE
    vulnerability_coverage_available = True
