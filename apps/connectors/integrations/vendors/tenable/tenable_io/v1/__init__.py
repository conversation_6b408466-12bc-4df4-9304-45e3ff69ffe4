from apps.connectors.integrations import TemplateVersion

from .connection import TenableIoV1Config, TenableIoV1Connection
from .integration import TenableIoV1Integration
from .settings import TenableIoV1Settings


class TenableIoV1TemplateVersion(TemplateVersion):
    integration = TenableIoV1Integration
    id = "v1"
    name = "v1"
    config_model = TenableIoV1Config
    connection_model = TenableIoV1Connection
    settings_model = TenableIoV1Settings
