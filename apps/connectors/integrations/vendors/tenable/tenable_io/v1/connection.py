from pydantic import Field
from tenable.io import TenableIO

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class TenableIoV1Config(TemplateVersionConfig):
    access_key: EncryptedStr = Field(
        title="Access Key",
        description="API Key used to authenticate with Tenable.io.",
    )
    secret_key: EncryptedStr = Field(
        title="Secret Key",
        description="API Key used to authenticate with Tenable.io.",
    )
    url: str = Field(
        title="Tenable URL Override",
        description=f"The default URL for fetching assets and vulnerabilities is {TenableIO._url}. "
        f"Enter a different URL if an alternate is required.",
        default=TenableIO._url,
    )


class TenableIoV1Connection(ConnectionTemplate):
    id = "tenable_io"
    name = "Tenable.io"
    config_model = TenableIoV1Config
