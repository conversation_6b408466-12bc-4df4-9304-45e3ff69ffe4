from tenable.io import TenableIO


class TenableIoV1Api:
    """
    Tenable.io API https://developer.tenable.com/reference
    pyTenable docs: https://pytenable.readthedocs.io
    """

    def __init__(self, access_key=None, secret_key=None, url=None, **kwargs):
        tenable_kwargs = {}
        if url:  # override default url
            tenable_kwargs["url"] = url

        # Remove deprecated attr to prevent warnings.
        # TODO: Remove this code when the tenable.io library
        #       removes the attrs.
        deprecated_attrs = [
            "access_groups",
            "access_groups_v2",
            "target_groups",
            "v3",
            "workbenches",
        ]
        for deprecated_attr in deprecated_attrs:
            if hasattr(TenableIO, deprecated_attr):
                delattr(TenableIO, deprecated_attr)
        self.client = TenableIO(access_key, secret_key, retries=0, **tenable_kwargs)

    def get_assets(self):
        # list does not have filtering
        # max return is 5000
        return self.client.assets.list()

    def export_assets(self, **kwargs):
        """
        See `client.exports.assets` for documented kwargs.
        Note: If you submit an asset export request with filters that are identical
              to a previously submitted asset export request then the old export is
              canceled and a new export is submitted.
        """
        return self.client.exports.assets(**kwargs)

    def export_vulns(self, **kwargs):
        """
        See `client.exports.vulns` for documented kwargs.
        Note: If you submit a vulnerability export request with filters that are identical
              to a previously submitted vulnerability export request then the old export is
              canceled and a new export is submitted.
        """
        return self.client.exports.vulns(**kwargs)

    def get_plugins(self, **kwargs):
        return self.client.plugins.list(**kwargs)

    def get_session_details(self):
        return self.client.session.details()

    def get_current_user_permissions(self):
        return self.client.access_control.get_current_user_permission()
