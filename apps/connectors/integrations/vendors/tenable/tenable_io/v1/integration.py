from requests.exceptions import ConnectionError
from restfly.errors import RestflyException

from apps.connectors.integrations import Integration

from .actions.detected_vulnerability_sync import TenableIoV1DetectedVulnerabilitySync
from .actions.host_sync import (
    TenableIoV1HostSync,
    TenableIoV1VulnerabilityAssetSync,
)
from .actions.vendor_vulnerability_sync import TenableIoV1VendorVulnerabilitySync
from .api import TenableIoV1Api
from .health_check import ConnectionHealthCheck


class TenableIoV1Integration(Integration):
    api_class = TenableIoV1Api
    exception_types = (RestflyException, ConnectionError)
    actions = (
        TenableIoV1HostSync,
        TenableIoV1VulnerabilityAssetSync,
        TenableIoV1DetectedVulnerabilitySync,
        TenableIoV1VendorVulnerabilitySync,
    )
    critical_health_checks = (ConnectionHealthCheck,)
