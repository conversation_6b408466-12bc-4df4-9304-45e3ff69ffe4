import logging
from datetime import timed<PERSON><PERSON>
from typing import Generator

from django.utils import timezone

from apps.connectors.integrations.actions import normalize, normalize_last_seen
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSync,
    HostSyncArgs,
    VulnerabilityAssetSync,
)
from apps.connectors.integrations.vendors.tenable.tenable_io.v1.health_check import (
    CanViewAllAssets,
)
from apps.connectors.utils import split_cs

logger = logging.getLogger(__name__)


def normalize_host(host_data: dict):
    # For now, we report all those errors for visibility, because we don't have good data yet.
    # This normalizer will be improved once we get real customer data.
    hostnames = host_data.get("hostnames") or host_data.get("netbios_names") or []
    hostnames = [h.lower().split(".")[0] for h in hostnames]
    fqdns = [f.lower() for f in host_data.get("fqdns") or []]
    if not hostnames:
        hostnames = list(sorted({f.split(".")[0] for f in fqdns}))

    if len(hostnames) > 1:
        logger.warning(
            "Found more than one hostname",
            extra={"integration": "tenable_io", "id": host_data.get("id")},
        )
    hostname = hostnames[0] if hostnames else ""
    if hostname:
        fqdns = [f for f in fqdns if f.startswith(hostname)]

    # Also populate os_family field. There is no os_family in the data,
    # so we will need to extract it from osPlatform
    os = host_data.get("operating_systems", [])
    if len(os) > 1:
        logger.info(
            "Found more than one OS",
            extra={"integration": "tenable_io", "id": host_data.get("id"), "os": os},
        )
    # The last OS in the list seems to be the latest install/upgrade.
    os_name = os[-1] if os else ""

    os_name = os_name.lower()
    # TODO
    # It's possible that host_data.get("system_types") will provide better host_type, but we don't have good data yet
    # and I could not find any useful information in the documentation
    ip_addresses = host_data.get("ipv4s") + host_data.get("ipv6s")
    # copy the mac addresses to avoid modifying the original list
    mac_addresses = list(host_data.get("mac_addresses")) or []

    def add_values(values, values_to_add):
        # Avoiding the use of set to preserve the order of the values
        for value in values_to_add:
            if value not in values:
                values.append(value)

    # Extract IPs, MAC addresses and FQDNs from network_interfaces
    # because it's possible that they are not present in the root attributes.
    for ni in host_data.get("network_interfaces") or []:
        add_values(ip_addresses, ni.get("ipv4s", []))
        add_values(ip_addresses, ni.get("ipv6s", []))
        add_values(mac_addresses, ni.get("mac_addresses", []))
        add_values(fqdns, ni.get("fqdns", []))

    return Host(
        source_id=host_data["id"],
        hostname=hostname,
        fqdns=fqdns,
        ip_addresses=ip_addresses,
        mac_addresses=mac_addresses,
        _os_name=os_name,
        last_seen=normalize_last_seen(host_data.get("last_seen")),
        source_data=host_data,
    )


class TenableIoV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        # See the documentation for the export_assets endpoint:
        # https://developer.tenable.com/reference/exports-assets-request-export
        params = {}
        api = self.integration.get_api()
        if self.settings.fetch_from_last_x_days:
            fetch_since = timezone.now() - timedelta(
                days=self.settings.fetch_from_last_x_days
            )
            params["updated_at"] = fetch_since.timestamp()

        if not self.settings.fetch_from_unauthenticated_scans:
            # we assume the epoch time will be less than any scan time
            params["last_authenticated_scan_time"] = 0

        if not self.settings.fetch_unlicensed_hosts:
            params["is_licensed"] = True

        if self.settings.fetch_by_tags:
            params["tags"] = []
            for tag in split_cs(self.settings.fetch_by_tags):
                parts = tag.split(":")
                params["tags"].append((parts[0], parts[1]))

        if self.settings.fetch_by_scan_ids:
            for scan_id in split_cs(self.settings.fetch_by_scan_ids):
                params["last_scan_id"] = scan_id
                yield from api.export_assets(**params)
        else:
            yield from api.export_assets(**params)

    def get_permission_checks(self):
        return [CanViewAllAssets]


class TenableIoV1VulnerabilityAssetSync(VulnerabilityAssetSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        # See the documentation for the export_assets endpoint:
        # https://developer.tenable.com/reference/exports-assets-request-export
        api = self.integration.get_api()
        yield from api.export_assets()

    def get_permission_checks(self):
        return [CanViewAllAssets]  # pragma: no cover
