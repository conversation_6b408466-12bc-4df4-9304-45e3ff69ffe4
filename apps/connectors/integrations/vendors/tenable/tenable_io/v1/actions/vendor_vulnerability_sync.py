import logging
from typing import Generator

from apps.connectors.integrations.actions import VendorVulnerabilitySync, normalize
from apps.connectors.integrations.actions.vendor_vulnerability_sync import (
    VendorVulnerability,
    VendorVulnerabilitySyncArgs,
)

logger = logging.getLogger(__name__)


def build_solution_markdown(solution: str, see_also: list) -> str:
    """
    Build the solution markdown text.
    """
    solution_text = solution
    if see_also:
        solution_text = f"{solution_text}\nSee Also:"
        for link in see_also:
            # add link in markdown format
            solution_text = f"{solution_text}\n  [{link}]({link})"
    return solution_text


def normalize_vendor_vuln(plugin: dict) -> VendorVulnerability:
    source_id = str(plugin["id"])
    technology_id = "tenable_io"
    title = plugin["name"]
    description = plugin["attributes"]["description"]

    cvss_v3_base_score = plugin["attributes"].get("cvss3_base_score")
    cvss_v2_base_score = plugin["attributes"].get("cvss2_base_score")

    patchable = plugin["attributes"].get("has_patch", False)

    solution = plugin["attributes"].get("solution", "")
    see_also = plugin["attributes"].get("see_also", [])
    solution_text = build_solution_markdown(solution, see_also)

    epss_score = None
    epss_percentile = None

    cves = plugin["attributes"].get("cve", [])

    return VendorVulnerability(
        source_id=source_id,
        technology_id=technology_id,
        title=title,
        description=description,
        cvss_v3_base_score=cvss_v3_base_score,
        cvss_v2_base_score=cvss_v2_base_score,
        epss_score=epss_score,
        epss_percentile=epss_percentile,
        cves=cves,
        patchable=patchable,
        solution=solution_text,
    )


class TenableIoV1VendorVulnerabilitySync(VendorVulnerabilitySync):
    @normalize(normalize_vendor_vuln)
    def execute(
        self, args: VendorVulnerabilitySyncArgs, **kwargs
    ) -> Generator[VendorVulnerability, None, None]:
        if args.since:
            kwargs["last_updated"] = args.since

        logger.info(f"Fetching vendor vulnerabilities", extra=kwargs)

        api = self.integration.get_api()
        yield from api.get_plugins(**kwargs)

    def get_permission_checks(self):
        return []
