from datetime import timed<PERSON><PERSON>
from typing import Generator

from django.utils import timezone

from apps.connectors.integrations.actions import normalize, normalize_last_seen, to_list
from apps.connectors.integrations.actions.detected_vulnerability_sync import (
    DetectedVulnerability,
    DetectedVulnerabilityStatus,
    DetectedVulnerabilitySync,
    DetectedVulnerabilitySyncArgs,
)
from apps.connectors.integrations.actions.host_sync import Host

# maps tenable status to active boolean
active_mapping = {
    "OPEN": True,
    "REOPENED": True,
    "FIXED": False,
}

# maps tenable status to DetectedVulnerabilityStatus
status_mapping = {
    "OPEN": DetectedVulnerabilityStatus.OPEN,
    "REOPENED": DetectedVulnerabilityStatus.REOPENED,
    "FIXED": DetectedVulnerabilityStatus.FIXED,
}


def normalize_asset(asset: dict):
    """
    Normalize Tenable asset data from Detected Vulnerability data to our Host schema.
    We can't reuse `normalize_host` from Host Sync action, because the format doesn't match
    """

    asset_source_id = asset["uuid"]

    hostname = asset["hostname"]
    os_list = asset.get("operating_system")
    os_name = os_list[0] if os_list else None
    mac_address = asset.get("mac_address")
    ip_addresses = []
    for ip in (asset.get("ipv4"), asset.get("ipv6")):
        if ip:
            ip_addresses.append(ip)

    last_authenticated_results = asset.get("last_authenticated_results")

    return Host(
        source_id=asset_source_id,
        hostname=hostname,
        ip_addresses=ip_addresses,
        mac_addresses=to_list(mac_address),
        _os_name=os_name,
        last_seen=normalize_last_seen(last_authenticated_results),
        source_data=asset,
    )


def normalize_detected_vulnerability(vuln: dict) -> DetectedVulnerability:
    technology_id = "tenable_io"
    vendor_vulnerability_source_id = str(vuln["plugin"]["id"])
    first_seen_at = vuln["first_found"]
    last_seen_at = vuln["last_found"]
    # Tenable does not return last_activated_at, so we use first_seen_at as an approximation
    last_activated_at = first_seen_at

    active = active_mapping[vuln["state"]]
    status = status_mapping[vuln["state"]]

    asset = normalize_asset(vuln["asset"])

    # No id in Tenable so we create a unique one based on the data
    port = vuln.get("port", {})
    port_number = port.get("port", 0)
    protocol = port.get("protocol", "None")
    source_id = (
        f"{asset.source_id}+{vendor_vulnerability_source_id}+{port_number}+{protocol}"
    )

    return DetectedVulnerability(
        source_id=source_id,
        technology_id=technology_id,
        vendor_vulnerability_source_id=vendor_vulnerability_source_id,
        first_seen_at=first_seen_at,
        last_seen_at=last_seen_at,
        last_activated_at=last_activated_at,
        active=active,
        status=status,
        asset=asset,
    )


class TenableIoV1DetectedVulnerabilitySync(DetectedVulnerabilitySync):
    @normalize(normalize_detected_vulnerability)
    def execute(
        self, args: DetectedVulnerabilitySyncArgs, **kwargs
    ) -> Generator[DetectedVulnerability, None, None]:
        # For an initial sync we want to get
        #   - any active detected vulns no matter how old (to handle the unusual case
        #     where a detection is open but hasn't had a change (e.g. a new detection
        #     in the last 30 days)
        #   - detected vulns that were fixed in the last 30 days
        #
        # For an incremental sync we want to get
        #   - any detected vulns that have had any update since the last sync (regardless
        #     of what that change was (status, new detection, etc.)

        initial_sync = False

        if args.since:
            # must pass the full list of status, otherwise Tenable assums you want
            # only "open" and "reopened"
            states = ["open", "reopened", "fixed"]
            kwargs["since"] = int(args.since.timestamp())
        else:
            initial_sync = True
            # get any active detections no matter how old
            states = [
                "open",
                "reopened",
            ]

        kwargs["state"] = states

        api = self.integration.get_api()
        yield from api.export_vulns(**kwargs)

        if initial_sync:
            # get any fixed detections from the last 30 days
            kwargs["state"] = [
                "fixed",
            ]
            since = timezone.now() - timedelta(days=30)
            kwargs["since"] = int(since.timestamp())
            yield from api.export_vulns(**kwargs)

    def get_permission_checks(self):
        return []
