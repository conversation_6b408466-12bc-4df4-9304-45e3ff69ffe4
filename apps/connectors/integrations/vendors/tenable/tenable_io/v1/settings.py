from pydantic import ConfigDict, Field, field_validator

from apps.connectors.integrations import create_settings_model
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.template import TemplateVersionActionSettings
from apps.connectors.utils import split_cs


class TenableIoV1HostSyncSettings(TemplateVersionActionSettings):
    model_config = ConfigDict(
        title="Tenable Vulnerability Management Fetch Hosts Settings",
    )

    fetch_from_last_x_days: int = Field(
        title="Fetch hosts updated in the last X days",
        description="Enter a value to fetch hosts updated within those number of days. "
        "A value of 0 will fetch all hosts (from the beginning of time).",
        default=30,
    )
    fetch_from_unauthenticated_scans: bool = Field(
        title="Fetch hosts from unauthenticated scans",
        description="Select whether to fetch hosts discovered by unauthenticated scans.",
        default=True,
    )
    fetch_unlicensed_hosts: bool = Field(
        title="Fetch unlicensed hosts",
        description="Licensed hosts are always fetched. Select whether to fetch unlicensed hosts.",
        default=True,
    )
    fetch_by_tags: str = Field(
        title="Fetch by tags",
        description="Specify a comma-separated list of tags in the format <category>:<value> to filter "
        "hosts in Tenable Vulnerability Management. Each tag consists of a category and a "
        "corresponding value, separated by a colon where <category> is replaced with the "
        "category name and <value> is replaced with the actual value. If supplied, this "
        "integration will fetch hosts that match any of the provided tags. "
        "Example: Workstations:East, Env:Prod",
        default_factory=str,
    )
    fetch_by_scan_ids: str = Field(
        title="Fetch by scan IDs",
        description="Specify a comma-separated list of scan IDs in Tenable Vulnerability Management. "
        "If supplied, this integration will only fetch hosts discovered by the scan IDs provided.",
        default_factory=str,
    )

    @field_validator("fetch_by_tags")
    @classmethod
    def force_tag_format(cls, tags):
        for tag in split_cs(tags):
            parts = tag.split(":")
            if len(parts) != 2:
                raise ValueError("Tags must be in the format <category>:<value>")
        return tags


TenableIoV1Settings = create_settings_model(
    "Tenable Vulnerability Management Advanced Settings",
    {
        IntegrationActionType.HOST_SYNC: TenableIoV1HostSyncSettings,
    },
)
