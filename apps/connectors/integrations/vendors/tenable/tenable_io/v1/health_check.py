from apps.connectors.integrations import IntegrationError
from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
)


class CanViewAllAssets(IntegrationPermissionsHealthCheck):
    name = "Can View All Assets"
    description = "Set Can View permissions for the All Assets object"
    value = "CanView AllAssets"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            permissions = self.integration.invoke("get_current_user_permissions")
            for granted in permissions["permissions_granted"]:
                if "CanView" in granted["actions"]:
                    types = set([obj["type"] for obj in granted["objects"]])
                    if "AllAssets" in types:
                        return IntegrationHealthCheckResult.PASSED
            return IntegrationHealthCheckResult.FAILED
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            self.integration.invoke("get_session_details")
            return IntegrationHealthCheckResult.PASSED
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED
