from apps.connectors.integrations import TemplateVersion

from .connection import VeritasAltaBaasV1Config, VeritasAltaBaasV1Connection
from .integration import VeritasAltaBaasV1Integration
from .settings import VeritasAltaBaasV1Settings


class VeritasAltaBaasV1TemplateVersion(TemplateVersion):
    integration = VeritasAltaBaasV1Integration
    id = "v1"
    name = "v1"
    config_model = VeritasAltaBaasV1Config
    connection_model = VeritasAltaBaasV1Connection
    settings_model = VeritasAltaBaasV1Settings
