from apps.connectors.integrations import ApiBase


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    yield response["data"]
    items_count = len(response["data"])
    page = 0
    while items_count > 0:
        page += items_count
        response = bound_method(**kwargs, offset=page)
        items_count = len(response["data"])
        if items_count > 0:
            yield response["data"]


class VeritasAltaBaasV1Api(ApiBase):
    def __init__(self, base_url=None, api_access_key=None):
        self.base_url = base_url
        self.api_access_key = api_access_key
        headers = {
            "Accept": "application/vnd.api+json; version=1.0",
            "Authorization": "Bearer " + api_access_key,
        }
        super().__init__(base_url=self.base_url, static_headers=headers)

    def get_assets(self, offset=0, limit=1000):
        workload = "default"
        asset_type = "all"
        url = f"baas-api/assetservice/workloads/{workload}/assets"
        return self.session.get(
            self.url(url),
            params={
                "assetType": asset_type,
                "page[offset]": offset,
                "page[limit]": limit,
            },
        ).json()

    def get_health_check(self):
        url = "baas-api/health/readiness"
        response = self.session.get(self.url(url))
        try:
            return response.json()
        except:
            return response.text
