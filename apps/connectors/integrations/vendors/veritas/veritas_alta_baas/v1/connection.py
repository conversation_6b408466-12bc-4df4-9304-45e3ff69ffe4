from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class VeritasAltaBaasV1Config(TemplateVersionConfig):
    # https://sort.veritas.com/public/documents/altabaas/1.2/general/productguides/html/getting-started/
    base_url: HttpUrl = Field(
        title="Base URL",
        description="The base URL for the Veritas Alta BaaS API",
    )
    api_access_key: EncryptedStr = Field(
        title="API Access Key",
        description="API Access Key for the Veritas Alta BaaS API",
    )


class VeritasAltaBaasV1Connection(ConnectionTemplate):
    id = "veritas_alta_baas"
    name = "Veritas Alta BaaS"
    config_model = VeritasAltaBaasV1Config
