from typing import Generator

from apps.connectors.integrations.actions import normalize, normalize_last_seen
from apps.connectors.integrations.actions.host_sync import (
    AssetCriticality,
    Host,
    HostSync,
    HostSyncArgs,
)
from apps.connectors.integrations.vendors.veritas.veritas_alta_baas.v1.api import (
    paginate,
)
from apps.connectors.integrations.vendors.veritas.veritas_alta_baas.v1.health_check import (
    ReadDeviceInventory,
)


def normalize_host(host_data: dict):
    # Logics goes here
    host_data_attributes = host_data.get("attributes")
    return Host(
        source_id=host_data_attributes.get("attributes").get("netbackupAssetId"),
        group_names=[],
        hostname=host_data_attributes.get("attributes").get("displayName"),
        fqdns=[host_data_attributes.get("attributes").get("serviceDomain")],
        ip_addresses=[
            host_data_attributes.get("attributes").get("attributes").get("privateIp")
        ],
        mac_addresses=[],
        _os_name=host_data_attributes.get("attributes")
        .get("attributes")
        .get("platform"),  # OsAttributes
        owners=[],  # List[OwnerAttributes]
        aad_id=None,
        criticality=AssetCriticality.UNKNOWN,
        last_seen=normalize_last_seen(
            host_data_attributes.get("attributes").get("lastDiscoveredTime")
        ),  # only works for iso date string or list of iso date strings
        source_data=host_data,
    )


class VeritasAltaBaasV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_assets, **kwargs):
            yield from page

    def get_permission_checks(self):
        return [ReadDeviceInventory]
