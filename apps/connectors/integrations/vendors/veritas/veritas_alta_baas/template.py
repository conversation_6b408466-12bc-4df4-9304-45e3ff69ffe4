from apps.connectors.integrations.template import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import VeritasAltaBaasV1TemplateVersion


class VeritasAltaBaasTemplate(Template):
    id = "veritas_alta_baas"
    name = "Veritas Alta BaaS API"
    category = Template.Category.ASSET_SOURCE
    versions = {
        VeritasAltaBaasV1TemplateVersion.id: VeritasAltaBaasV1TemplateVersion(),
    }
    vendor = Vendors.VERITAS
