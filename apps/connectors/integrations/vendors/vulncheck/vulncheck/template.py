from apps.connectors.integrations.template import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import VulnCheckV1TemplateVersion


class VulnCheckTemplate(Template):
    is_internal = True
    id = "vulncheck"
    name = "VulnCheck"
    category = Template.Category.VULNERABILITY_MANAGEMENT
    versions = {
        VulnCheckV1TemplateVersion.id: VulnCheckV1TemplateVersion(),
    }
    vendor = Vendors.VULNCHECK
