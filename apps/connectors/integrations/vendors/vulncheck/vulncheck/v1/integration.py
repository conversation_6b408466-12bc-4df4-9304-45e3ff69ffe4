from apps.connectors.integrations import Integration

from .actions.vulnerability_intelligence_sync.vulnerability_intelligence import (
    VulnCheckV1VulnerabilityIntelligenceSync,
)
from .api import VulnCheckV1Api
from .health_check import ConnectionHealthCheck


class VulnCheckV1Integration(Integration):
    api_class = VulnCheckV1Api
    actions = (VulnCheckV1VulnerabilityIntelligenceSync,)
    critical_health_checks = (ConnectionHealthCheck,)

    # FIXME: This is the original implementation, but it's not working because of API errors
    #  For now, we will use the backup API to get the data
    # @Integration.register_action(IntegrationAction.VULNERABILITY_INTELLIGENCE_SYNC)
    # def list_cve_data(
    #     self, VulnCheckV1Settings, args: VulnerabilityIntelligenceSyncArgs, **kwargs
    # ) -> Generator[CVE, None, None]:
    #     params = {"limit": MAX_PAGE_SIZE}
    #     if args.since:
    #         params["lastModStartDate"] = args.since.strftime("%Y-%m-%d")
    #
    #     api = self.get_api()
    #     for page in paginate(
    #         api.get_index_data_cursor, index="vulncheck-nvd2", params=params
    #     ):
    #         cve_ids = ",".join({item["id"] for item in page})
    #         exploits_index_data = api.get_index_data(
    #             index="exploits", params={"cve": cve_ids, "limit": MAX_PAGE_SIZE}
    #         )
    #         exploits_index_data_by_cve = {
    #             item["id"]: item for item in exploits_index_data["data"]
    #         }
    #
    #         for nvd2_data in page:
    #             exploits_data = exploits_index_data_by_cve.get(nvd2_data["id"])
    #             cve = normalize_cve(nvd2_data, exploits_data)
    #             if cve:
    #                 yield cve
    #             else:
    #                 continue
