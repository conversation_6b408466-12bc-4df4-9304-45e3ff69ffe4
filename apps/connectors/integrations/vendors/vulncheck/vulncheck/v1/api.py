from apps.connectors.integrations import ApiBase

MAX_PAGE_SIZE = 2000


def paginate_cursor(bound_method, params=None, **kwargs):
    params = params or {}
    response = bound_method(params=params, **kwargs)
    yield response["data"]

    while cursor := response["_meta"].get("next_cursor"):
        params["cursor"] = cursor
        response = bound_method(params=params, **kwargs)

        yield response["data"]


# def paginate_page(bound_method, params=None, **kwargs):
#     params = params or {}
#     response = bound_method(params=params, **kwargs)
#     yield response["data"]
#
#     while page := response["_meta"]["page"] < response["_meta"].get("total_pages", 1):
#         page += 1
#         params["page"] = page
#
#         response = bound_method(params=params, **kwargs)
#         yield response["data"]


def paginate(bound_method, params=None, **kwargs):
    if "cursor" in bound_method.__doc__:
        yield from paginate_cursor(bound_method, params=params, **kwargs)
    # else:
    #     yield from paginate_page(bound_method, params=params, **kwargs)


class VulnCheckV1Api(ApiBase):
    """
    VulnCheck API client
    https://docs.vulncheck.com/api
    """

    def __init__(self, token=None, **kwargs):
        url = "https://api.vulncheck.com/v3/"

        static_headers = {
            "Accept": "application/json",
            "Authorization": f"Bearer {token}",
        }
        super().__init__(static_headers=static_headers, base_url=url)

    def get_index_data(self, index, params=None):
        """
        Retrieve a list of all documents from the given index.
        https://docs.vulncheck.com/api/indice
        """
        url = self.url(f"index/{index}")
        return self.session.get(url, params=params).json()

    def get_index_data_cursor(self, index, params=None):
        """
        Retrieve a list of all documents from the given index.
        https://docs.vulncheck.com/api/cursor
        """
        url = self.url(f"index/{index}/cursor")
        return self.session.get(url, params=params).json()

    def get_index_backup(self, index, params=None):
        """
        Return an object including a download URL for an existing specific offline backup.
        https://docs.vulncheck.com/api/backup
        """
        url = self.url(f"backup/{index}")
        return self.session.get(url, params=params).json()
