from apps.connectors.integrations import TemplateVersion

from .connection import VulnCheckV1Config, VulnCheckV1Connection
from .integration import VulnCheckV1Integration
from .settings import VulnCheckV1Settings


class VulnCheckV1TemplateVersion(TemplateVersion):
    integration = VulnCheckV1Integration
    id = "v1"
    name = "v1"
    config_model = VulnCheckV1Config
    connection_model = VulnCheckV1Connection
    settings_model = VulnCheckV1Settings
