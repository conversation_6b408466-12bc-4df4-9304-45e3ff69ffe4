from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class VulnCheckV1Config(TemplateVersionConfig):
    token: EncryptedStr = Field(
        title="API token", description="API token issued by VulnCheck."
    )


class VulnCheckV1Connection(ConnectionTemplate):
    id = "vulncheck"
    name = "VulnCheck"
    config_model = VulnCheckV1Config
