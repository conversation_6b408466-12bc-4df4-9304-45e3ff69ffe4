import logging
from enum import StrEnum, auto

from pydantic import BaseModel, field_validator

from apps.connectors.integrations.actions.vulnerability_intelligence_sync import CVE

logger = logging.getLogger(__name__)


class ExploitType(StrEnum):
    INITIAL_ACCESS = auto()
    REMOTE_WITH_CREDENTIALS = auto()
    LOCAL = auto()
    CLIENT_SIDE = auto()
    INFO_LEAK = auto()
    DENIAL_OF_SERVICE = auto()

    NOT_AVAILABLE = auto()
    OTHER = auto()


class Exploit(BaseModel):
    name: str
    type: ExploitType

    @field_validator("type", mode="before")
    def validate_type(cls, value):  # noqa: N805
        if value is None:
            return ExploitType.NOT_AVAILABLE

        value = value.replace("-", "_").upper()
        if value == "REMOTE_WITH_CREDS":
            return ExploitType.REMOTE_WITH_CREDENTIALS
        if value == "INITIAL":
            return ExploitType.INITIAL_ACCESS
        if value == "INFOLEAK":
            return ExploitType.INFO_LEAK

        try:
            return ExploitType[value]
        except KeyError:
            logger.error("Unknown exploit type", extra={"exploit_type": value})
            return ExploitType.OTHER


def parse_cvss_data(metric_data: list[dict]) -> dict:
    primary_cvss_data = [cvss for cvss in metric_data if cvss["type"] == "Primary"]
    result = primary_cvss_data[0] if primary_cvss_data else metric_data[0]
    return result["cvssData"]


def parse_nvd2_index_data(data: dict) -> CVE:
    source_id = data["id"]
    description = ""
    for item in data.get("descriptions", []):
        if item["lang"] == "en":
            description = item["value"]
            break

    cvss_v3_base_score = None
    cvss_v2_base_score = None
    epss_score = None
    epss_percentile = None
    epss_last_modified = None
    for metric_name, metric_data in data["metrics"].items():
        if metric_name.startswith("cvssMetricV3"):
            cvss_data = parse_cvss_data(metric_data)
            cvss_v3_base_score = cvss_data["baseScore"]
        if metric_name.startswith("cvssMetricV2"):
            cvss_data = parse_cvss_data(metric_data)
            cvss_v2_base_score = cvss_data["baseScore"]
        if metric_name == "epss":
            epss_score = metric_data["epss_score"]
            epss_percentile = metric_data["epss_percentile"]
            epss_last_modified = metric_data["last_modified"]

    return CVE(
        source_id=source_id,
        description=description,
        cvss_v3_base_score=cvss_v3_base_score,
        cvss_v2_base_score=cvss_v2_base_score,
        epss_score=epss_score,
        epss_percentile=epss_percentile,
        epss_last_modified=epss_last_modified,
    )


def normalize_cve(nvd2_data: dict, exploits_data: dict | None) -> CVE | None:
    try:
        cve = parse_nvd2_index_data(nvd2_data)

        if not exploits_data:
            return cve

        exploits = [
            Exploit(name=e["name"], type=e.get("exploit_type"))
            for e in exploits_data.get("exploits", [])
        ]

        found_exploits = {exploit_type: False for exploit_type in ExploitType}
        for exploit in exploits:
            found_exploits[exploit.type] = True

        if found_exploits[ExploitType.INITIAL_ACCESS]:
            cve.exploited_initial_access = True
        if found_exploits[ExploitType.REMOTE_WITH_CREDENTIALS]:
            cve.exploited_remote_with_credentials = True
        if found_exploits[ExploitType.LOCAL]:
            cve.exploited_local = True
        if found_exploits[ExploitType.CLIENT_SIDE]:
            cve.exploited_client_side = True
        if found_exploits[ExploitType.INFO_LEAK]:
            cve.exploited_info_leak = True
        if found_exploits[ExploitType.DENIAL_OF_SERVICE]:
            cve.exploited_denial_of_service = True

        cve.commercial_exploit_found = exploits_data.get(
            "commercial_exploit_found", False
        )
        cve.weaponized_exploit_found = exploits_data.get(
            "weaponized_exploit_found", False
        )
        cve.reported_exploited = exploits_data.get("reported_exploited", False)
        cve.reported_exploited_by_threat_actors = exploits_data.get(
            "reported_exploited_by_threat_actors", False
        )
        cve.reported_exploited_by_ransomware = exploits_data.get(
            "reported_exploited_by_ransomware", False
        )
        cve.reported_exploited_by_botnets = exploits_data.get(
            "reported_exploited_by_botnets", False
        )
        cve.in_cisa_kev = exploits_data.get("inKEV", False)
        cve.in_vulncheck_kev = exploits_data.get("inVCKEV", False)

        return cve
    except Exception:
        logger.exception(
            "Error parsing CVE data", extra={"cve_id": nvd2_data.get("id")}
        )
        return None
