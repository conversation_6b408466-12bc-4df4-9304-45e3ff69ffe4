import contextlib
import json
import logging
import os
import shutil
import tempfile
import zipfile
from typing import Generator

from ata_common.http import TimeoutSession

from apps.connectors.integrations.actions.vulnerability_intelligence_sync import CVE
from apps.connectors.integrations.vendors.vulncheck.vulncheck.v1.actions.vulnerability_intelligence_sync.cve import (
    normalize_cve,
)

logger = logging.getLogger(__name__)

CHUNK_SIZE = 50000000  # 50 MB


@contextlib.contextmanager
def download_backup_index(index: str, backup_url) -> str:
    try:
        with tempfile.NamedTemporaryFile(delete=False) as tf:
            session = TimeoutSession()
            with session.get(backup_url, stream=True) as response:
                response.raise_for_status()
                for chunk in response.iter_content(chunk_size=CHUNK_SIZE):
                    tf.write(chunk)

        with zipfile.ZipFile(tf.name, "r") as zip_ref:
            tmp_directory = os.path.join(
                tempfile.gettempdir(), f"{index}-{os.urandom(8).hex()}"
            )
            zip_ref.extractall(tmp_directory)
        os.remove(tf.name)

        yield tmp_directory
    finally:
        shutil.rmtree(tmp_directory)


def normalize_backup_index_data(
    nvd2_directory: str, exploits_directory: str
) -> Generator[CVE, None, None]:
    for nvd2_file in os.listdir(nvd2_directory):
        try:
            with open(os.path.join(nvd2_directory, nvd2_file)) as f:
                backup_data = json.loads(f.read())
                # Each backup file contains exactly one CVE
                nvd2_data = backup_data["results"][0]
                cve_id = nvd2_data["id"]
                exploits_file = os.path.join(
                    exploits_directory, f"exploits-{cve_id}.json"
                )

                try:
                    with open(exploits_file) as f:
                        exploits_data = json.loads(f.read())
                except FileNotFoundError:
                    exploits_data = None

            cve = normalize_cve(nvd2_data, exploits_data)
            if cve:
                yield cve
            else:
                continue
        except Exception:
            logger.exception(f"Error processing backup file", extra={"file": nvd2_file})
            continue
