from typing import Generator

from apps.connectors.integrations.actions import VulnerabilityIntelligenceSync
from apps.connectors.integrations.actions.vulnerability_intelligence_sync import (
    CVE,
    VulnerabilityIntelligenceSyncArgs,
)
from apps.connectors.integrations.vendors.vulncheck.vulncheck.v1.actions.vulnerability_intelligence_sync.backup_index_data import (
    download_backup_index,
    normalize_backup_index_data,
)


class VulnCheckV1VulnerabilityIntelligenceSync(VulnerabilityIntelligenceSync):
    def execute(
        self, args: VulnerabilityIntelligenceSyncArgs, **kwargs
    ) -> Generator[CVE, None, None]:
        api = self.integration.get_api()

        nvd2_backup_url = api.get_index_backup("vulncheck-nvd2")["data"][0]["url"]
        with download_backup_index("vulncheck-nvd2", nvd2_backup_url) as nvd2_directory:
            exploits_backup_url = api.get_index_backup("exploits")["data"][0]["url"]
            with download_backup_index(
                "exploits", exploits_backup_url
            ) as exploits_directory:
                yield from normalize_backup_index_data(
                    nvd2_directory, exploits_directory
                )

    def get_permission_checks(self, *args, **kwargs):
        return []
