from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.vendors.microsoft.connection import (
    MicrosoftConfig,
    MicrosoftConnection,
)

from .bookmarks import MicrosoftUsocV1Bookmarks
from .integration import MicrosoftUsocV1Integration
from .settings import MicrosoftUsocV1Settings


class MicrosoftUsocV1TemplateVersion(TemplateVersion):
    integration = MicrosoftUsocV1Integration
    id = "v1"
    name = "v1"
    connection_model = MicrosoftConnection
    config_model = MicrosoftConfig
    settings_model = MicrosoftUsocV1Settings
    bookmarks_model = MicrosoftUsocV1Bookmarks
