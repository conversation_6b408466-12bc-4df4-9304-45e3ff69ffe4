from typing import Generator

from apps.connectors.integrations.actions.ioc.list_iocs import ListIocs, ListIocsArgs
from apps.connectors.integrations.actions.ioc.schemas.ioc import IOC, IOCStatus
from apps.connectors.integrations.actions.utils import normalize
from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.api import (
    MicrosoftUsocV1Api,
)
from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.health_check import (
    ReadWriteCustomDetections,
)


def normalize_ioc(ioc: dict) -> IOC:
    return IOC(
        source_data=ioc,
        source_id=ioc["detectorId"],
        status=IOCStatus.ACTIVE if ioc["isEnabled"] else IOCStatus.INACTIVE,
        title=ioc["displayName"],
        description=ioc["displayName"],
        query=ioc["queryCondition"]["queryText"],
    )


class MicrosoftUsocV1ListIocs(ListIocs):
    @normalize(normalize_ioc)
    def execute(self, args: ListIocsArgs, **kwargs) -> Generator[IOC, None, None]:
        api: MicrosoftUsocV1Api = self.integration.get_api()
        api.client.URL = api.client.BETA

        params = {}
        if args.source_ids:
            params["$filter"] = "detectorId in ({})".format(
                ",".join("'{}'".format(i) for i in args.source_ids)
            )

        for rule in api.enumerate(api.list_detection_rules, params=params):
            yield rule

    def get_permission_checks(self, *args, **kwargs):
        return [ReadWriteCustomDetections]
