from apps.connectors.integrations.actions.add_alert_comment import (
    AddAlertComment,
    AddAlertCommentArgs,
    AddAlertCommentResult,
)
from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.api import (
    MicrosoftUsocV1Api,
)
from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.health_check import (
    ReadWriteAlerts,
)


class MicrosoftUsocV1AddAlertComment(AddAlertComment):
    def execute(self, args: AddAlertCommentArgs, **kwargs) -> AddAlertCommentResult:
        api: MicrosoftUsocV1Api = self.integration.get_api()
        api.create_incident_comment(args.vendor_sync_id, args.comment)

        return AddAlertCommentResult()

    def get_permission_checks(self, *args, **kwargs):
        return [ReadWriteAlerts]
