import datetime
import logging
from typing import Generator

import numpy as np

from apps.connectors.integrations.actions import normalize
from apps.connectors.integrations.actions.event_sync import (
    Event,
    EventIOCInfo,
    EventSync,
    EventSyncArgs,
    VendorRef,
)
from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.api import (
    MicrosoftUsocV1Api,
)
from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.bookmarks import (
    MicrosoftUsocV1EventSyncBookmark,
)
from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.health_check import (
    ReadAlerts,
)
from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.settings import (
    MicrosoftUsocV1EventSyncSettings,
)

logger = logging.getLogger(__name__)


def format_datetime(dt: np.datetime64) -> str:
    return str(np.datetime_as_string(dt, timezone="UTC"))


def parse_datetime(dt: np.datetime64) -> np.datetime64:
    return np.datetime64(dt.rstrip("Z"))


def normalize_event(event: dict) -> Event:
    return Event(
        raw_event=event,
        event_timestamp=event["lastUpdateDateTime"],
        ioc=EventIOCInfo(
            external_id=event["detectorId"],
            external_name=event["title"],
            has_ioc_definition=event["detectionSource"] == "customDetection",
            mitre_techniques=event["mitreTechniques"],
        ),
        vendor_item_ref=VendorRef(
            id=event["id"],
            title=event["title"],
            url=event.get("alertWebUrl", ""),
            created=event["createdDateTime"],
        ),
        vendor_group_ref=VendorRef(
            id=event["incidentId"],
            title=event["incident"].get("displayName", ""),
            url=event["incident"].get("incidentWebUrl", ""),
            created=event["incident"].get("createdDateTime"),
        ),
    )


def get_service_sources(settings: MicrosoftUsocV1EventSyncSettings) -> list[str]:
    sources = [
        "microsoftDefenderForEndpoint" if settings.fetch_mde else None,
        "microsoftDefenderForIdentity" if settings.fetch_mdi else None,
        "microsoftDefenderForCloudApps" if settings.fetch_mdca else None,
        "microsoftDefenderForOffice365" if settings.fetch_mdo else None,
        "microsoft365Defender" if settings.fetch_365d else None,
        "azureAdIdentityProtection" if settings.fetch_idp else None,
        "microsoftAppGovernance" if settings.fetch_app_governance else None,
        "dataLossPrevention" if settings.fetch_dlp else None,
        "microsoftDefenderForCloud" if settings.fetch_mdc else None,
        "microsoftSentinel" if settings.fetch_sentinel else None,
    ]

    return [s for s in sources if s]


class MicrosoftUsocV1EventSync(EventSync):
    settings: MicrosoftUsocV1EventSyncSettings

    @normalize(normalize_event)
    def execute(
        self,
        args: EventSyncArgs,
        bookmark: MicrosoftUsocV1EventSyncBookmark = None,
        **kwargs,
    ) -> Generator[Event, None, None]:
        api: MicrosoftUsocV1Api = self.integration.get_api()

        headers = {
            "Prefer": "include-unknown-enum-members",
        }

        # There are some subtle edge cases related to paging, in which an alert being updated midway through paging, causing its own page to rise (no
        # problem since we'd just get a dupe of it) and causing the alert whose place it's taking to fall to an earlier page (problem since we may have already
        # retrieved that page).  To avoid this, we sort the query by lastUpdateDateTime *desc* and set the upper bound to the current time.
        query_upper_bound = datetime.datetime.now().isoformat() + "Z"

        params = {
            "$select": "*",
            "$filter": f"lastUpdateDateTime gt {bookmark.latest_event_update_datetime} and lastUpdateDateTime lt {query_upper_bound} and serviceSource in ('"
            + "', '".join(get_service_sources(self.settings))
            + "')",
            "$orderby": "lastUpdateDateTime desc",
        }

        last_update_datetime = parse_datetime(bookmark.latest_event_update_datetime)
        alerts = []
        for alert in api.enumerate(api.get_alerts, params=params, headers=headers):
            current = parse_datetime(alert["lastUpdateDateTime"])
            if current > last_update_datetime:
                last_update_datetime = current

            alerts.append(alert)

        # Enrich each alert with its incident info.
        # To grab the incident info, we make a follow up call to the incidents endpoint, batching to avoid hitting
        # the 414 error (URI too long).  In any case, the Incidents endpoint has a max page size of 50 so we can't really get any
        # efficiencies from a large batch size.
        unique_incident_ids = list({a["incidentId"] for a in alerts})
        incidents = {}
        batch_size = 250
        batches = [
            unique_incident_ids[i : i + batch_size]
            for i in range(0, len(unique_incident_ids), batch_size)
        ]
        for batch in batches:
            incident_params = {
                "$filter": f"id in ('" + "', '".join(batch) + "')",
            }
            for incident in api.enumerate(
                api.get_incidents, params=incident_params, headers=headers
            ):
                incidents[incident["id"]] = incident

        for alert in alerts:
            alert["incident"] = incidents.get(alert["incidentId"])
            yield alert

        bookmark.latest_event_update_datetime = format_datetime(last_update_datetime)

    def get_permission_checks(self):
        return [ReadAlerts]
