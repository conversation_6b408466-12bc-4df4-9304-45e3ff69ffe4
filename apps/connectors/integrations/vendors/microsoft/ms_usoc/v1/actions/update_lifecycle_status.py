from apps.connectors.integrations.actions.update_lifecycle_status import (
    CorrIncidentStatus,
    CorrVerdict,
    UpdateLifecycleStatus,
    UpdateLifecycleStatusArgs,
    UpdateLifecycleStatusResult,
)
from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.api import (
    MicrosoftUsocV1Api,
)
from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.health_check import (
    ReadWriteAlerts,
)


def map_corr_incident_status(status: CorrIncidentStatus) -> str:
    return {
        CorrIncidentStatus.NEW: "new",
        CorrIncidentStatus.ASSIGNED: "inProgress",
        CorrIncidentStatus.REVIEWING: "inProgress",
        CorrIncidentStatus.MITIGATED: "resolved",
        CorrIncidentStatus.CLOSED: "resolved",
    }[status]


def map_corr_verdict(verdict: CorrVerdict) -> str:
    return {
        CorrVerdict.BENIGN_TRUE_POSITIVE: "informationalExpectedActivity",
        CorrVerdict.TRUE_POSITIVE: "truePositive",
        CorrVerdict.FALSE_POSITIVE: "falsePositive",
    }[verdict]


class MicrosoftUsocV1UpdateLifecycleStatus(UpdateLifecycleStatus):
    def execute(
        self, args: UpdateLifecycleStatusArgs, **kwargs
    ) -> UpdateLifecycleStatusResult:
        api: MicrosoftUsocV1Api = self.integration.get_api()

        update_data = {
            "status": map_corr_incident_status(args.status),
        }

        if args.assigned_to or update_data["status"] == "resolved":
            update_data["assignedTo"] = args.assigned_to

        if args.verdict:
            update_data["classification"] = map_corr_verdict(args.verdict)

        if args.comment:
            api.create_alert_comment(args.vendor_sync_id, args.comment)

        api.update_alert(args.vendor_sync_id, update_data)

        return UpdateLifecycleStatusResult()

    def get_permission_checks(self, *args, **kwargs):
        return [ReadWriteAlerts]
