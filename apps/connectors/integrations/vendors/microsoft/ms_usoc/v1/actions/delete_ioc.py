from apps.connectors.integrations.actions.ioc.delete_ioc import (
    <PERSON>ete<PERSON><PERSON>,
    <PERSON>eteIoc<PERSON>rg<PERSON>,
    DeleteIocResult,
)
from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.api import (
    MicrosoftUsocV1Api,
)
from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.health_check import (
    ReadWriteCustomDetections,
)


class MicrosoftUsocV1DeleteIoc(DeleteIoc):
    def execute(self, args: DeleteIocArgs, **kwargs) -> DeleteIocResult:
        api: MicrosoftUsocV1Api = self.integration.get_api()
        api.client.URL = api.client.BETA

        rule_id = self._lookup_rule_id(args.source_id)
        api.delete_detection_rule(rule_id)

        return DeleteIocResult()

    def get_permission_checks(self, *args, **kwargs):
        return [ReadWriteCustomDetections]

    def _lookup_rule_id(self, detector_id: str):
        api: MicrosoftUsocV1Api = self.integration.get_api()
        api.client.URL = api.client.BETA

        return next(
            filter(
                lambda rule: rule["detectorId"] == detector_id,
                api.enumerate(api.list_detection_rules),
            ),
            {},
        ).get("id")
