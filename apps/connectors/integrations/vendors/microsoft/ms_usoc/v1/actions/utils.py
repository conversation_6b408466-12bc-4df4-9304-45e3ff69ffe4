from collections.abc import Generator
from typing import Dict

from django.template import Context, Template
from pydantic import BaseModel

from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.api import (
    MicrosoftUsocV1Api,
)


class QueryActionMixin:
    def execute_query(self, query: str, args: BaseModel) -> Generator[Dict, None, None]:
        api: MicrosoftUsocV1Api = self.integration.get_api()

        query = Template(query).render(Context(args.model_dump()))
        response = api.run_hunting_query(query, timespan=None)

        yield from response["results"]
