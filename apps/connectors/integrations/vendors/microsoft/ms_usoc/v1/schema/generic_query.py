from typing import Optional

from pydantic import BaseModel, ConfigDict, Field

from apps.connectors.integrations.schemas.identifiers import UserIdentifier


class GenericQueryResponse(BaseModel):
    model_config = ConfigDict(title="Generic Query Response")
    row: dict


class TimeRange(BaseModel):
    start_time: str = Field(
        title="Start Time",
        description="The earliest record to fetch.",
    )

    end_time: Optional[str] = Field(
        title="End Time",
        description="The latest record to fetch.",
        default=None,
    )


class BaseQueryArgs(BaseModel):
    time_range: Optional[TimeRange] = Field(
        title="Time Range",
        description="The time range to fetch activity for.",
        default=None,
    )


class UserQueryArgs(BaseQueryArgs):
    identifier: UserIdentifier = Field(
        title="User Identifier",
        description="The user identifier to fetch activity for.",
    )
