from pydantic import ConfigDict, Field

from apps.connectors.integrations import IntegrationActionType, create_settings_model
from apps.connectors.integrations.template import TemplateVersionActionSettings


class MicrosoftUsocV1EventSyncSettings(TemplateVersionActionSettings):
    model_config = ConfigDict(title="Microsoft Unified Fetch Events Settings")

    fetch_mde: bool = Field(
        title="Fetch MDE events",
        description="Select whether to fetch MDE events.",
        default=True,
    )

    fetch_mdi: bool = Field(
        title="Fetch MDI events",
        description="Select whether to fetch MDI events.",
        default=True,
    )

    fetch_mdca: bool = Field(
        title="Fetch MDCA events",
        description="Select whether to fetch MDCA events.",
        default=True,
    )

    fetch_mdo: bool = Field(
        title="Fetch MDO events",
        description="Select whether to fetch MDO events.",
        default=True,
    )

    fetch_365d: bool = Field(
        title="Fetch 365D events",
        description="Select whether to fetch 365D events.",
        default=True,
    )

    fetch_idp: bool = Field(
        title="Fetch IDP events",
        description="Select whether to fetch IDP events.",
        default=True,
    )

    fetch_app_governance: bool = Field(
        title="Fetch App Governance events",
        description="Select whether to fetch App Governance events.",
        default=True,
    )

    fetch_dlp: bool = Field(
        title="Fetch DLP events",
        description="Select whether to fetch DLP events.",
        default=True,
    )

    fetch_mdc: bool = Field(
        title="Fetch MDC events",
        description="Select whether to fetch MDC events.",
        default=True,
    )

    fetch_sentinel: bool = Field(
        title="Fetch Sentinel events",
        description="Select whether to fetch Sentinel events.",
        default=True,
    )


MicrosoftUsocV1Settings = create_settings_model(
    "MicrosoftUsocV1Settings",
    {IntegrationActionType.EVENT_SYNC: MicrosoftUsocV1EventSyncSettings},
)
