from microsoft_client.exceptions import MicrosoftHTTPError

from apps.connectors.integrations import Integration
from apps.connectors.integrations.mixins import AadClientMixin
from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.actions.add_alert_comment import (
    MicrosoftUsocV1AddAlertComment,
)
from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.actions.delete_ioc import (
    MicrosoftUsocV1DeleteIoc,
)
from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.actions.event_sync import (
    MicrosoftUsocV1EventSync,
)
from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.actions.list_data_sources import (
    MicrosoftUsocV1ListDataSources,
)
from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.actions.list_iocs import (
    MicrosoftUsocV1ListIocs,
)
from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.actions.save_ioc import (
    MicrosoftUsocV1SaveIoc,
)
from apps.connectors.integrations.vendors.microsoft.ms_usoc.v1.actions.update_lifecycle_status import (
    MicrosoftUsocV1UpdateLifecycleStatus,
)

from .api import MicrosoftUsocV1Api
from .health_check import ConnectionHealthCheck


class MicrosoftUsocV1Integration(AadClientMixin, Integration):
    api_class = MicrosoftUsocV1Api
    exception_types = (MicrosoftHTTPError,)
    actions = (
        MicrosoftUsocV1EventSync,
        MicrosoftUsocV1UpdateLifecycleStatus,
        MicrosoftUsocV1DeleteIoc,
        MicrosoftUsocV1ListDataSources,
        MicrosoftUsocV1ListIocs,
        MicrosoftUsocV1SaveIoc,
        MicrosoftUsocV1AddAlertComment,
    )
    critical_health_checks = (ConnectionHealthCheck,)
