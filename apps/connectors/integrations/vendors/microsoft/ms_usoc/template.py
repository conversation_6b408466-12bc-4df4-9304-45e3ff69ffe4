from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import MicrosoftUsocV1TemplateVersion


class MicrosoftUsocTemplate(Template):
    id = "ms_usoc"
    name = "Microsoft Unified SOC"
    category = Template.Category.OPERATIONAL_TECHNOLOGY
    versions = {
        MicrosoftUsocV1TemplateVersion.id: MicrosoftUsocV1TemplateVersion(),
    }
    is_internal = False
    vendor = Vendors.MICROSOFT
