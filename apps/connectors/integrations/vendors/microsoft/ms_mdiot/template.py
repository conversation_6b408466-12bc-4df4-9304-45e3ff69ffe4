from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import MsMdiotV1TemplateVersion


class MsMdiotTemplate(Template):
    id = "ms_mdiot"
    name = "Microsoft Defender for IoT"
    category = Template.Category.ENDPOINT_SECURITY
    versions = {
        MsMdiotV1TemplateVersion.id: MsMdiotV1TemplateVersion(),
    }
    vendor = Vendors.MICROSOFT
