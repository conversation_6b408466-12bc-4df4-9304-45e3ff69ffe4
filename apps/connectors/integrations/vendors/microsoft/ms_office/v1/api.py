from microsoft_client.graph import MicrosoftGraphClient


class MsOfficeV1Api:
    def __init__(self, tenant_id=None, client_id=None, client_secret=None, **kwargs):
        self.client = MicrosoftGraphClient(
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret,
        )

        self.enumerate = self.client.enumerate
        self.paginate = self.client.paginate

    def list_message_rules(self, user_id, params=None, headers=None):
        """
        Retrieve a list of message rule objects for the specified user
        """
        return self.client.request(
            "GET",
            f"/users/{user_id}/mailFolders/inbox/messageRules",
            params=params,
            headers=headers,
        )

    def delete_message_rule(self, user_id, rule_id, params=None):
        """
        Delete a message rule in the specified user's mailbox
        """
        return self.client.request(
            "DELETE",
            f"/users/{user_id}/mailFolders/inbox/messageRules/{rule_id}",
            params=params,
        )

    def get_messages(self, user_id, mail_folder=None, params=None):
        """
        Get messages from the user's mailbox.
        """

        path = f"/users/{user_id}"
        if mail_folder:
            path += f"/mailFolders/{mail_folder}/messages"
        else:
            path += "/messages"

        return self.client.request(
            "GET",
            path,
            params=params,
        )

    def delete_message(self, user_id, message_id):
        """
        Delete a message from the user's mailbox.
        """
        return self.client.request(
            "DELETE",
            f"/users/{user_id}/messages/{message_id}",
        )

    def move_message(self, user_id, message_id, folder):
        """
        Move a message to a different folder in the user's mailbox.
        """
        return self.client.request(
            "POST",
            f"/users/{user_id}/messages/{message_id}/move",
            json={"destinationId": folder},
        )

    def get_permissions(self):
        return self.client.permissions

    def has_permission(self, permission):
        return self.client.has_permission(permission)
