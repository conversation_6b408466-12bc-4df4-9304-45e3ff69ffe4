from microsoft_client.exceptions import MicrosoftHTTPError

from apps.connectors.integrations import Integration
from apps.connectors.integrations.mixins import AadClientMixin
from apps.connectors.integrations.vendors.microsoft.ms_office.v1.actions.delete_email import (
    MsOfficeV1DeleteEmail,
)
from apps.connectors.integrations.vendors.microsoft.ms_office.v1.actions.delete_mailbox_rule import (
    MsOfficeV1DeleteMailboxRule,
)
from apps.connectors.integrations.vendors.microsoft.ms_office.v1.actions.restore_email import (
    MsOfficeV1RestoreEmail,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.health_check import (
    ConnectionHealthCheck,
)

from .api import MsOfficeV1Api


class MsOfficeV1Integration(AadClientMixin, Integration):
    api_class = MsOfficeV1Api
    exception_types = (MicrosoftHTTPError,)
    actions = (
        MsOfficeV1DeleteEmail,
        MsOfficeV1RestoreEmail,
        MsOfficeV1DeleteMailboxRule,
    )
    critical_health_checks = (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,)
