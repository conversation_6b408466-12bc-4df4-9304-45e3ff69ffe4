from apps.connectors.integrations.actions.email import (
    DeleteEmailAction,
    EmailActionArgs,
    EmailActionResult,
)
from apps.connectors.integrations.schemas import ErrorDetail, Message
from apps.connectors.integrations.vendors.microsoft.ms_office.v1.api import (
    MsOfficeV1Api,
)
from apps.connectors.integrations.vendors.microsoft.ms_office.v1.health_check import (
    ReadWriteMailMessage,
)


class MsOfficeV1DeleteEmail(DeleteEmailAction):
    """
    Delete email action for Microsoft Office 365 integration.
    """

    def execute(self, args: EmailActionArgs, **kwargs) -> EmailActionResult:
        api: MsOfficeV1Api = self.integration.get_api()

        params = {
            f"$filter": f"internetMessageId eq '{args.message_id.value}'",
            "$select": "id",
        }

        messages = api.get_messages(args.user_id.value, params=params)["value"]
        if not messages:
            return EmailActionResult(
                error=ErrorDetail(
                    message=f"Message with ID {args.message_id.value} not found."
                )
            )

        api.delete_message(args.user_id.value, messages[0]["id"])

        return EmailActionResult(
            result=Message(message=f"Message with ID {args.message_id.value} deleted.")
        )

    def get_permission_checks(self, *args, **kwargs):
        return [ReadWriteMailMessage]
