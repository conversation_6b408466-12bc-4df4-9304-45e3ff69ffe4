from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import MsSentV1TemplateVersion


class MsSentTemplate(Template):
    id = "ms_sent"
    name = "Microsoft Sentinel"
    category = Template.Category.OPERATIONAL_TECHNOLOGY
    versions = {
        MsSentV1TemplateVersion.id: MsSentV1TemplateVersion(),
    }
    vendor = Vendors.MICROSOFT
