import logging
from datetime import datetime
from enum import StrEnum
from typing import Generator

from dateutil import parser

from apps.connectors.integrations.actions import normalize, to_list
from apps.connectors.integrations.actions.event_sync import (
    Event,
    EventIOCInfo,
    EventSync,
    EventSyncArgs,
)
from apps.connectors.integrations.schemas.ocsf import (
    ControlAction,
    Disposition,
    Email,
    EmailActivity,
    EmailActivityType,
    File,
    Metadata,
    Profile,
    Url,
)
from apps.connectors.integrations.vendors.abnormal.abnormal_security.v1.api import (
    AbnormalSecurityV1Api,
    paginate,
)
from apps.connectors.integrations.vendors.abnormal.abnormal_security.v1.bookmarks import (
    AbnormalSecurityV1EventSyncBookmark,
)

logger = logging.getLogger(__name__)


class AbnormalRemediationStatus(StrEnum):
    AUTO_REMEDIATED = "Auto Remediated"
    POST_REMEDIATED = "Post Remediated"
    MARKED_SAFE = "Marked Safe"
    REMEDIATION_TRIGGERED = "Remediation Triggered"
    REMEDIATED = "Remediated"
    REMEDIATION_ATTEMPTED = "Remediation Attempted(Message not Found)"
    WOULD_REMEDIATE = "Would Remediate"


def format_datetime(dt: datetime) -> str:
    # YYYY-MM-DDTHH:MM:SSZ
    return dt.strftime("%Y-%m-%dT%H:%M:%SZ")


def parse_datetime(dt_str: str) -> datetime:
    return parser.isoparse(dt_str)


def convert_remediation_status(status: str):
    action = ControlAction.ALLOWED
    if status == AbnormalRemediationStatus.AUTO_REMEDIATED:
        action = ControlAction.DENIED

    match status:
        case AbnormalRemediationStatus.AUTO_REMEDIATED | AbnormalRemediationStatus.REMEDIATED | AbnormalRemediationStatus.POST_REMEDIATED:
            disposition = Disposition.QUARANTINED
        case AbnormalRemediationStatus.MARKED_SAFE:
            disposition = Disposition.EXONERATED
        case AbnormalRemediationStatus.REMEDIATION_TRIGGERED:
            disposition = Disposition.CUSTOM_ACTION
        case AbnormalRemediationStatus.REMEDIATION_ATTEMPTED:
            disposition = Disposition.ERROR
        case AbnormalRemediationStatus.WOULD_REMEDIATE:
            disposition = Disposition.DETECTED
        case _:
            disposition = Disposition.UNKNOWN

    return action, disposition


def convert_to_ocsf(event: dict, title: str) -> EmailActivity:
    action, disposition = convert_remediation_status(event["remediationStatus"])
    return EmailActivity(
        activity=EmailActivityType.SCAN,
        action=action,
        disposition=disposition,
        message=title,
        metadata=Metadata(
            correlation_uid=event["threatId"],
            event_code=None,
            profiles=[Profile.DATETIME, Profile.OSINT, Profile.SECURITY_CONTROL],
            uid=event["threatId"],
        ),
        email=Email(
            cc=event["ccEmails"],
            delivered_to_list=[event["recipientAddress"]],
            files=[File(name=name) for name in event["attachmentNames"]],
            from_=event["fromAddress"],
            is_read=event["isRead"],
            message_uid=event["internetMessageId"],
            reply_to_mailboxes=event["replyToEmails"],
            subject=event["subject"],
            to=event["toAddresses"],
            uid=event["abxMessageIdStr"],
            urls=[Url(url_string=url) for url in event["urls"]],
            x_originating_ip=to_list(event["senderIpAddress"]),
        ),
        time_dt=event["receivedTime"],
    )


def get_event_field(event: dict, field_name: str) -> str | None:
    val = event.get(field_name, None)
    if val == "Other":
        return None
    return val


def normalize_event(event: dict) -> Event:
    attack_type = get_event_field(event, "attackType")
    attack_vector = get_event_field(event, "attackVector")
    attack_strategy = get_event_field(event, "attackStrategy")
    attack_party = get_event_field(event, "attackParty")

    ioc_name = f"Suspicious Email Detected - {attack_type or 'Unclassified Threat'}"
    if attack_vector:
        ioc_name += f" via {attack_vector}"
    if attack_strategy:
        ioc_name += f" ({attack_strategy})"

    attack_tuple = (attack_type, attack_strategy, attack_vector, attack_party)
    return Event(
        raw_event=event,
        event_timestamp=event["receivedTime"],
        ioc=EventIOCInfo(
            external_id=".".join(a or "Other" for a in attack_tuple),
            external_name=ioc_name,
            has_ioc_definition=False,
            mitre_techniques=None,
        ),
        vendor_item_ref=None,
        vendor_group_ref=None,
        ocsf=convert_to_ocsf(event, ioc_name),
    )


class AbnormalSecurityV1EventSync(EventSync):
    @normalize(normalize_event)
    def execute(
        self,
        args: EventSyncArgs,
        bookmark: AbnormalSecurityV1EventSyncBookmark = None,
        **kwargs,
    ) -> Generator[Event, None, None]:
        api: AbnormalSecurityV1Api = self.integration.get_api()

        max_remediation_timestamp = parse_datetime(bookmark.latest_time_remediated)

        params = {
            "filter": f"latestTimeRemediated gte {bookmark.latest_time_remediated}",
            "pageNumber": 1,
            "pageSize": 100,
        }
        for response in paginate(api.get_threats, params=params):
            for threat in response["threats"]:
                threat_id = threat["threatId"]
                t_params = {"pageNumber": 1, "pageSize": 100}
                for t_response in paginate(api.get_threat, threat_id, params=t_params):
                    for message in t_response["messages"]:
                        yield message

                        if dt := message.get("remediationTimestamp"):
                            dt = parse_datetime(dt)
                            if max_remediation_timestamp < dt:
                                max_remediation_timestamp = dt

        bookmark.latest_time_remediated = format_datetime(max_remediation_timestamp)

    def get_permission_checks(self):
        return []  # pragma: no cover
