import csv
import mimetypes
from io import StringIO

import openpyxl
import requests
from criticalstart.artifacts.sdk.sdk_client import SDKClient
from criticalstart.artifacts.sdk.sdk_config import SDKConfig
from django.conf import settings
from django.core.files.base import ContentFile

from apps.connectors.integrations.integration import IntegrationError

EXCEL_MIME_TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"

mimetypes.add_type(EXCEL_MIME_TYPE, ".xlsx")


class ImportHostsV1Api:
    def __init__(self, artifact_id=None, **kwargs):
        self.artifact_id = artifact_id
        config = SDKConfig(
            service_url=settings.ARTIFACTS_SERVICE_URL,
            auth_url=settings.ATA_AUTH_MICROSERVICE_URL,
            auth_secret=settings.ATA_AUTH_MICROSERVICE_SECRET_KEY,
        )
        self._client = SDKClient(
            token="TOKEN",
            sdk_config=config,
        )

    def import_hosts(self):
        artifact = self._client.artifacts.internal_get_artifact(str(self.artifact_id))
        mime_type, _ = mimetypes.guess_type(artifact.name)
        response = requests.get(artifact.url)
        if response.status_code != 200:
            raise IntegrationError(f"Failed to download file from {artifact.url}")
        file_content = response.content
        if mime_type == EXCEL_MIME_TYPE:
            return self._import_from_excel(file_content)
        else:
            return self._import_from_csv(file_content)

    @staticmethod
    def _import_from_excel(file_content):
        file = ContentFile(file_content)
        wb = openpyxl.load_workbook(file)
        ws = wb[wb.sheetnames[0]]
        cols = []
        for col in ws.iter_cols():
            cols.append(col[0].value)
        rows = []
        for row in ws.iter_rows(min_row=2, values_only=True):
            onerow = {}
            for rowval in row:
                onerow[cols[row.index(rowval)]] = rowval
            rows.append(onerow)
        return rows

    @staticmethod
    def _import_from_csv(file_content):
        content = file_content.decode(encoding="utf-8")
        file = StringIO(content)
        reader = csv.reader(file)
        rows = []
        cols = next(reader)
        for row in reader:
            onerow = {}
            for rowval in row:
                onerow[cols[row.index(rowval)]] = rowval if rowval != "" else None
            rows.append(onerow)
        return rows
