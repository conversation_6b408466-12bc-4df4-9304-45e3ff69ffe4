from typing import Generator

from apps.connectors.integrations.actions.host_sync import (
    AssetCriticality,
    Host,
    HostSync,
    HostSyncArgs,
)
from apps.connectors.integrations.actions.utils import (
    normalize,
    normalize_last_seen,
    to_list,
)
from apps.connectors.integrations.schemas.operating_system import (
    HostType,
    OsAttributes,
    OsFamily,
)
from apps.connectors.integrations.vendors.veeam.veeam.v1.api import paginate
from apps.connectors.integrations.vendors.veeam.veeam.v1.health_check import (
    ReadAllHosts,
)


def normalize_host(host_data: dict):
    os_name = host_data.get("platform")
    host_type = HostType.from_os_name(os_name)
    os_family, __ = OsFamily.from_string(os_name)

    return Host(
        source_id=host_data.get("backupAgentId"),
        group_names=[],
        hostname=host_data.get("name"),
        fqdns=[],
        ip_addresses=to_list(host_data.get("ipAddresses")),
        mac_addresses=[],
        os=OsAttributes(host_type=host_type, family=os_family, name=os_name),
        owners=[],
        aad_id=None,
        criticality=AssetCriticality.UNKNOWN,
        last_seen=normalize_last_seen(host_data.get("lastProtectedDate")),
        source_data=host_data,
    )


class VeeamV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_all_backup_agents, **kwargs):
            yield from page

    def get_permission_checks(self):
        return [ReadAllHosts]
