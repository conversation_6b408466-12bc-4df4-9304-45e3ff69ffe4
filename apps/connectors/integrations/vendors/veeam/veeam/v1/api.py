from apps.connectors.integrations import ApiBase


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    yield response["items"]
    offset = 0
    while True:
        offset = offset + 1
        response = bound_method(**kwargs, offset=offset)
        if ("items" in response) and len(response["items"]) > 0:
            yield response["items"]
        else:
            break


class VeeamV1Api(ApiBase):
    def __init__(self, url=None, username=None, password=None, **kwargs):
        self.username = username
        self.password = password
        super().__init__(base_url=url)

    def get_access_token(self):
        data = {
            "grant_type": "password",
            "username": self.username,
            "password": self.password,
        }

        self.session.headers.update(
            {"Content-Type": "application/x-www-form-urlencoded; charset=utf-8"}
        )
        response = self.session.post(self.url("/api/token"), data=data)

        return response.json()["access_token"]

    def ensure_token(self):
        if not hasattr(self, "_token"):
            self._token = self.get_access_token()
            self.session.headers.update(
                {"Authorization": f"Bearer {self._token}"},
            )
        return self._token

    def get_all_backup_agents(self, limit=100, offset=0):
        query_params = {"Limit": limit, "Offset": offset}
        self.ensure_token()

        url_path = self.url("/api/v2.2/vbr/backupAgents")
        response = self.session.get(url_path, params=query_params)

        return response.json()
