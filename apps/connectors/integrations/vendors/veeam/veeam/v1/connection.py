from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig


class VeeamV1Config(TemplateVersionConfig):
    url: HttpUrl = Field(
        title="URL",
        description="Veeam URL",
    )
    username: str = Field(
        title="Username",
        description="Veeam Username",
    )
    password: str = Field(
        title="Password",
        description="Veeam Password",
    )


class VeeamV1Connection(ConnectionTemplate):
    id = "veeam"
    name = "Veeam"
    config_model = VeeamV1Config
