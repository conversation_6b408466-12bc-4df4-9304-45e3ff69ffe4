from apps.connectors.integrations.integration import Integration
from apps.connectors.integrations.vendors.veeam.veeam.v1.actions.host_sync import (
    VeeamV1HostSync,
)
from apps.connectors.integrations.vendors.veeam.veeam.v1.api import VeeamV1Api
from apps.connectors.integrations.vendors.veeam.veeam.v1.health_check import (
    ConnectionHealthCheck,
)


class VeeamV1Integration(Integration):
    api_class = VeeamV1Api
    actions = (VeeamV1HostSync,)
    critical_health_checks = (ConnectionHealthCheck,)
