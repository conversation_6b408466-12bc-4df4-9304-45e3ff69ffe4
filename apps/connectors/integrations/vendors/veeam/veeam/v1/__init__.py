from apps.connectors.integrations import TemplateVersion

from .connection import VeeamV1Config, VeeamV1Connection
from .integration import VeeamV1Integration
from .settings import VeeamV1Settings


class VeeamV1TemplateVersion(TemplateVersion):
    integration = VeeamV1Integration
    id = "v1"
    name = "v1"
    config_model = VeeamV1Config
    connection_model = VeeamV1Connection
    settings_model = VeeamV1Settings
