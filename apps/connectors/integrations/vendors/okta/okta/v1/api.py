from apps.connectors.integrations.api import ApiBase


class OktaV1Api(ApiBase):
    def __init__(self, org_url=None, api_token=None):
        static_headers = {
            "Authorization": f"SSWS {api_token}",
        }
        super().__init__(static_headers=static_headers, base_url=org_url)

    def get_org_settings(self):
        url = self.url(f"api/v1/org")
        return self.session.get(url).json()

    def enable_user_login(self, user_id: str):
        url = self.url(f"api/v1/users/{user_id}/lifecycle/unsuspend")
        return self.session.post(url).json()

    def disable_user_login(self, user_id: str):
        url = self.url(f"api/v1/users/{user_id}/lifecycle/suspend")
        return self.session.post(url).json()

    def reset_user_password(self, user_id: str, params=None):
        url = self.url(f"api/v1/users/{user_id}/lifecycle/reset_password")
        return self.session.post(url, params=params).json()

    def revoke_user_sessions(self, user_id: str):
        url = self.url(f"api/v1/users/{user_id}/sessions")
        return self.session.delete(url)

    def get_sign_in_logs(self, params=None):
        url = self.url(f"api/v1/logs")
        return self.session.get(url, params=params).json()

    def get_user(self, user_id: str):
        url = self.url(f"api/v1/users/{user_id}")
        return self.session.get(url).json()

    def get_my_user(self):
        url = self.url(f"api/v1/users/me?expand=blocks")
        return self.session.get(url).json()

    def get_user_roles(self, user_id: str):
        url = self.url(f"api/v1/users/{user_id}/roles?expand=targets/groups")
        return self.session.get(url).json()
