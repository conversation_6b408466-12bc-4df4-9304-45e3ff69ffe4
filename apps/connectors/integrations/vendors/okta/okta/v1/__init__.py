from apps.connectors.integrations import TemplateVersion

from .connection import OktaV1Config, OktaV1Connection
from .integration import OktaV1Integration
from .settings import OktaV1Settings


class OktaV1TemplateVersion(TemplateVersion):
    integration = OktaV1Integration
    id = "v1"
    name = "v1"
    config_model = OktaV1Config
    connection_model = OktaV1Connection
    settings_model = OktaV1Settings
