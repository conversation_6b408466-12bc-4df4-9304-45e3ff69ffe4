import logging
from datetime import datetime
from typing import Generator, Optional

from apps.connectors.integrations.actions.event_sync import (
    Event,
    EventIOCInfo,
    EventSync,
    EventSyncArgs,
    VendorRef,
)
from apps.connectors.integrations.actions.utils import normalize
from apps.connectors.integrations.schemas.ocsf import (
    Authentication,
    Device,
    GeoLocation,
    Metadata,
    NetworkEndpoint,
    Product,
    Session,
    Severity,
    StatusId,
    User,
    UserType,
)
from apps.connectors.integrations.schemas.ocsf.enums import OSType
from apps.connectors.integrations.schemas.ocsf.objects.autonomous_system import (
    AutonomousSystem,
)
from apps.connectors.integrations.schemas.ocsf.objects.http_request import HttpRequest
from apps.connectors.integrations.schemas.ocsf.objects.operating_system import (
    OperatingSystem,
)
from apps.connectors.integrations.vendors.okta.okta.v1.api import OktaV1Api
from apps.connectors.integrations.vendors.okta.okta.v1.bookmarks import (
    OktaV1EventSyncBookmark,
)
from apps.connectors.integrations.vendors.okta.okta.v1.health_check import (
    OktaSystemLogsRead,
)

logger = logging.getLogger(__name__)


def convert_to_ocsf(event: dict) -> Authentication:
    """
    Normalize an Okta System Log event to OCSF Authentication Activity format.

    Handles all Okta Identity Threat Protection (ITP) event types and maps them
    according to the specified field mapping requirements.

    Args:
        event: Raw Okta System Log event

    Returns:
        Authentication: OCSF normalized event
    """
    # Extract basic event information
    event_type = event.get("eventType", "")
    published = event.get("published", "")
    display_message = event.get("displayMessage", "")
    outcome = event.get("outcome", {})

    # Determine message - prefer outcome.reason for security.threat.detected events
    message = display_message
    if outcome.get("reason"):
        message = outcome["reason"]  #

    # Map severity according to specification
    severity_mapping = {
        "DEBUG": Severity.INFORMATIONAL,
        "INFO": Severity.INFORMATIONAL,
        "WARN": Severity.MEDIUM,
        "ERROR": Severity.HIGH,
    }
    severity = severity_mapping.get(
        event.get("severity", "INFO"), Severity.INFORMATIONAL
    )

    # Map status according to specification
    status_mapping = {
        "SUCCESS": StatusId.SUCCESS,
        "FAILURE": StatusId.FAILURE,
        "SKIPPED": StatusId.OTHER,
        "ALLOW": StatusId.SUCCESS,
        "DENY": StatusId.FAILURE,
        "CHALLENGE": StatusId.OTHER,
        "DEFERRED": StatusId.OTHER,
        "SCHEDULED": StatusId.OTHER,
        "ABANDONED": StatusId.OTHER,
        "UNANSWERED": StatusId.OTHER,
    }
    status_id = status_mapping.get(outcome.get("result", ""), StatusId.UNKNOWN)  #

    # Create metadata
    metadata = Metadata(
        event_code=event_type,  #
        uid=event.get("uuid"),  #
        product=Product(
            version=event.get("version"),  #
        ),
    )

    # Extract actor information
    actor = _extract_actor(event.get("actor", {}))

    # Extract source endpoint (client) information
    src_endpoint = _extract_src_endpoint(event.get("client", {}))

    dst_endpoint = NetworkEndpoint(
        svc_name=event.get("debugContext", {}).get("debugData", {}).get("url"),
    )

    # Extract session information
    session = _extract_session(event.get("authenticationContext", {}))

    http_request = HttpRequest(
        user_agent=event.get("client", {}).get("userAgent", {}).get("rawUserAgent"),
        uid=event.get("debugContext", {}).get("debugData", {}).get("requestId"),
    )

    device = _extract_device(event.get("device", {}))

    return Authentication(
        message=message,  #
        metadata=metadata,  #
        severity=severity,  #
        status_id=status_id,  #
        time=published,  #
        actor=actor,  #
        src_endpoint=src_endpoint,  #
        dst_endpoint=dst_endpoint,  #
        session=session,  #
        http_request=http_request,  #
        device=device,  #
    )


def normalize_event(event: dict) -> Event:
    """
    Normalize an Okta System Log event to the Event structure.

    Args:
        event: Raw Okta System Log event

    Returns:
        Event: Normalized event with OCSF data
    """
    # Convert to OCSF
    ocsf = convert_to_ocsf(event)

    # Extract event timestamp
    event_timestamp = datetime.fromisoformat(
        event.get("published", "").replace("Z", "+00:00")
    )

    # Create Event object following CrowdStrike pattern
    result = Event(
        event_timestamp=event_timestamp,
        raw_event=event,
        ocsf=ocsf,
        vendor_item_ref=VendorRef(
            id=event.get("uuid", ""),
            title=event.get("displayMessage", ""),
            url=None,  # Okta doesn't provide direct links to events
            created=event_timestamp,
        ),
        vendor_group_ref=None,
        ioc=EventIOCInfo(
            external_id=event.get("eventType", ""),
            external_name=event.get("displayMessage", ""),
            has_ioc_definition=False,
            mitre_techniques=None,  # Okta events don't map to MITRE techniques
        ),
    )
    return result


def _extract_actor(actor_data: dict) -> Optional[User]:
    """Extract actor information from Okta event."""
    if not actor_data:
        return None

    return User(
        email_addr=actor_data.get("target", [{}])[0].get("alternateId"),
        display_name=actor_data.get("target", [{}])[0].get("displayName"),
        uid=actor_data.get("target", [{}])[0].get("id"),
        type=actor_data.get("target", [{}])[0].get("type"),
    )


def _extract_device(device_data: dict) -> Optional[Device]:
    """Extract device information from Okta event."""
    if not device_data:
        return None

    return Device(
        uid=device_data.get("id"),  #
        name=device_data.get("name"),  #
        os=OperatingSystem(
            name=device_data.get("os_platform"),  #
        ),
        is_managed=device_data.get("managed"),  #
    )


def _extract_src_endpoint(client_data: dict) -> Optional[Device]:
    """Extract source endpoint information from client data."""
    if not client_data:
        return None

    return NetworkEndpoint(
        type=client_data.get("device"),  #
        location=GeoLocation(
            city=client_data.get("geographicalContext", {}).get("city"),  #
            country=client_data.get("geographicalContext", {}).get("country"),  #
            lat=client_data.get("geographicalContext", {})
            .get("geolocation", {})
            .get("lat"),  #
            long=client_data.get("geographicalContext", {})
            .get("geolocation", {})
            .get("lon"),  #
            postal_code=client_data.get("geographicalContext", {}).get("postalCode"),  #
            region=client_data.get("geographicalContext", {}).get("state"),  #
            isp=client_data.get("securityContext", {}).get("isp"),  #
        ),
        ip=client_data.get("ipAddress"),  #
        os=OperatingSystem(
            name=client_data.get("userAgent", {}).get("os"),  #
            type=client_data.get("userAgent", {}).get("os"),  #
            type_id=OSType.OTHER,  #
        ),
        autonomous_system=AutonomousSystem(
            number=client_data.get("securityContext", {}).get("asNumber"),  #
            name=client_data.get("securityContext", {}).get("asOrg"),  #
        ),
        domain=client_data.get("securityContext", {}).get("domain"),  #
    )


def _extract_session(auth_context: dict) -> Optional[Session]:
    """Extract session information from authentication context."""
    if not auth_context:
        return None

    # Build unmapped data for ITP-specific context
    unmapped_data = {}

    # Standard authentication context fields
    if auth_context.get("externalSessionId"):
        unmapped_data["external_session_id"] = auth_context["externalSessionId"]
    if auth_context.get("authenticationStep"):
        unmapped_data["authentication_step"] = auth_context["authenticationStep"]
    if auth_context.get("credentialProvider"):
        unmapped_data["credential_provider"] = auth_context["credentialProvider"]
    if auth_context.get("credentialType"):
        unmapped_data["credential_type"] = auth_context["credentialType"]
    if auth_context.get("interface"):
        unmapped_data["interface"] = auth_context["interface"]

    # ITP-specific fields
    if auth_context.get("authenticationProvider"):
        unmapped_data["authentication_provider"] = auth_context[
            "authenticationProvider"
        ]

    return Session(
        uid=auth_context.get("externalSessionId"),  #
        issuer=auth_context.get("issuer").get("id"),  #
        unmapped=unmapped_data if unmapped_data else None,
    )


class OktaV1EventSync(EventSync):
    """
    Okta System Log event synchronization for security events.

    Fetches security-related events from Okta System Log API and normalizes
    them to OCSF Authentication Activity format.
    """

    PAGE_SIZE = 1000  # Maximum allowed by Okta API

    @normalize(normalize_event)
    def execute(
        self,
        args: EventSyncArgs,
        bookmark: OktaV1EventSyncBookmark = None,
        **kwargs,
    ) -> Generator[Event, None, None]:
        """
        Fetch security events from Okta System Log API.

        Args:
            args: Event sync arguments
            bookmark: Bookmark tracking last processed event
            **kwargs: Additional arguments

        Yields:
            Event: Normalized OCSF events
        """
        api: OktaV1Api = self.integration.get_api()

        # Build filter for security events
        event_filter = self._build_event_filter()

        # Prepare query parameters
        params = {
            "since": bookmark.latest_event_published_datetime,
            "filter": event_filter,
            "limit": self.PAGE_SIZE,
            "sortOrder": "ASCENDING",  # Process events chronologically
        }

        logger.info(
            f"Fetching Okta security events since {bookmark.latest_event_published_datetime}"
        )

        latest_published = bookmark.latest_event_published_datetime
        event_count = 0

        try:
            # Use the enumerate method for automatic pagination
            for event in api.enumerate_system_log_events(params):

                event_count += 1

                # Update latest published timestamp
                event_published = event.get("published", "")
                if event_published > latest_published:
                    latest_published = event_published

                yield event

        except Exception as e:
            logger.error(f"Error fetching Okta events: {str(e)}")
            raise

        # Update bookmark with latest timestamp
        bookmark.latest_event_published_datetime = latest_published

        logger.info(f"Processed {event_count} Okta security events")

    def _build_event_filter(self) -> str:
        """
        Build SCIM filter expression for security events.

        Returns:
            str: SCIM filter expression
        """
        # Create filter for security event types
        event_type_filters = [
            f'eventType eq "{event_type}"' for event_type in self.SECURITY_EVENT_TYPES
        ]

        # Combine with OR operator
        return " or ".join(event_type_filters)

    def get_permission_checks(self):
        """Return required permission checks for this action."""
        return [OktaSystemLogsRead]
