from requests.auth import HTTP<PERSON>asicAuth

from apps.connectors.integrations import ApiBase


class ProofpointV1Api(ApiBase):
    def __init__(self, url=None, principal=None, secret=None):
        self.base_url = url
        self.principal = principal
        self.secret = secret

        super().__init__(
            base_url=self.base_url,
            static_headers={
                "Accept": "application/json",
                "Content-Type": "application/json",
            },
        )
        self.session.auth = HTTPBasicAuth(self.principal, self.secret)

    def get_all_siem_events(self, since_time: str):
        return self.session.get(
            self.url("/v2/siem/all?format=json&sinceTime=" + since_time)
        ).json()

    def get_email_threat(self, threat_id: str):
        return self.session.get(self.url(f"/v2/threat/summary/{threat_id}")).json()

    def decode_url(self, urls: list):
        return self.session.post(self.url("/v2/url/decode"), json={"urls": urls}).json()

    def get_threat_forensics(self, threat_id: str):
        return self.session.get(self.url(f"/v2/forensics?threatId={threat_id}")).json()
