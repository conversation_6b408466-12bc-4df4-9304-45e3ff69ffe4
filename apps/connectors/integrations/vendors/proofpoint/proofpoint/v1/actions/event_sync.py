import logging
from datetime import datetime
from typing import Generator

from dateutil import parser

from apps.connectors.integrations.actions import normalize
from apps.connectors.integrations.actions.event_sync import (
    Event,
    EventIOCInfo,
    EventSync,
    EventSyncArgs,
)
from apps.connectors.integrations.actions.utils import to_list
from apps.connectors.integrations.schemas.ocsf import (
    OSINT,
    ControlAction,
    Disposition,
    Email,
    EmailActivity,
    EmailActivityType,
    File,
    Fingerprint,
    HashAlgorithm,
    HttpHeader,
    Metadata,
    NetworkActivity,
    NetworkActivityType,
    NetworkEndpoint,
    OSINTIndicatorType,
    Profile,
    Url,
    UrlCategory,
)
from apps.connectors.integrations.vendors.proofpoint.proofpoint.v1.api import (
    ProofpointV1Api,
)
from apps.connectors.integrations.vendors.proofpoint.proofpoint.v1.bookmarks import (
    ProofpointV1EventSyncBookmark,
)

logger = logging.getLogger(__name__)


def format_datetime(dt: datetime) -> str:
    # YYYY-MM-DDTHH:MM:SSZ
    return dt.strftime("%Y-%m-%dT%H:%M:%SZ")


def parse_datetime(dt_str: str) -> datetime:
    return parser.isoparse(dt_str)


def convert_message_type(classification: str):
    match classification.lower():
        case "attachment":
            return OSINTIndicatorType.FILE
        case "url":
            return OSINTIndicatorType.URL
        case "message":
            return OSINTIndicatorType.EMAIL
        case _:
            return classification


def convert_message_to_ocsf(
    event: dict,
    message_type: str,
    title: str,
) -> EmailActivity:
    http_headers = []
    # Conditionally add "Sender" header if the value exists
    sender = event.get("sender")
    if sender:
        http_headers.append(HttpHeader(name="Sender", value=sender))

    # Conditionally add "X-Mailer" header if the value exists
    xmailer = event.get("xmailer")
    if xmailer:
        http_headers.append(HttpHeader(name="X-Mailer", value=xmailer))

    blocked = "Blocked" in message_type
    return EmailActivity(
        activity=EmailActivityType.RECEIVE,
        action=ControlAction.DENIED if blocked else ControlAction.ALLOWED,
        disposition=Disposition.BLOCKED if blocked else Disposition.ALLOWED,
        message=title,
        metadata=Metadata(
            correlation_uid=event.get("GUID"),
            event_code=None,
            profiles=[Profile.DATETIME, Profile.OSINT, Profile.SECURITY_CONTROL],
            uid=event.get("GUID"),
        ),
        email=Email(
            cc=to_list(event.get("ccAddresses")),
            from_=event.get("fromAddress"),
            uid=event.get("GUID"),
            from_mailbox=event.get("headerFrom"),
            reply_to_mailboxes=to_list(event.get("headerReplyTo")),
            message_uid=event.get("messageID"),
            size=event.get("messageSize"),
            to=to_list(event.get("recipient")),
            reply_to=event.get("replyToAddress"),
            http_headers=http_headers,
            x_originating_ip=to_list(event.get("senderIP")),
            subject=event.get("subject"),
            files=[
                File(
                    name=message_part.get("filename"),
                    hashes=[
                        Fingerprint(
                            algorithm=HashAlgorithm.MD5,
                            value=message_part.get("md5"),
                        ),
                        Fingerprint(
                            algorithm=HashAlgorithm.SHA256,
                            value=message_part.get("sha256"),
                        ),
                    ],
                    mime_type=message_part.get("oContentType"),
                    security_descriptor=message_part.get("sandboxStatus"),
                )
                for message_part in event.get("messageParts", [])
            ],
        ),
        osint=[
            OSINT(
                name=threat_info_map.get("classification"),
                value=threat_info_map.get("threat"),
                uid=threat_info_map.get("threatId"),
                type=convert_message_type(threat_info_map.get("threatType")),
                src_url=threat_info_map.get("threatUrl"),
            )
            for threat_info_map in event.get("threatsInfoMap", [])
        ],
        time_dt=event.get("messageTime"),
    )


def convert_click_classification(classification: str):
    match classification.lower():
        case "phish":
            return UrlCategory.PHISHING
        case "spam":
            return UrlCategory.SPAM
        case _:
            return classification


def convert_click_to_ocsf(
    event: dict,
    message_type: str,
    title: str,
) -> NetworkActivity:
    blocked = "Blocked" in message_type
    return NetworkActivity(
        activity=NetworkActivityType.REFUSE if blocked else NetworkActivityType.OPEN,
        message=title,
        metadata=Metadata(
            correlation_uid=event.get("GUID"),
            event_code=None,
            profiles=[Profile.DATETIME, Profile.OSINT, Profile.SECURITY_CONTROL],
            uid=event.get("GUID"),
        ),
        dst_endpoint=NetworkEndpoint(ip=event.get("clickIP")),
        url=Url(
            url_string=event.get("url"),
            categories=[convert_click_classification(event.get("classification"))],
        )
        if event.get("url")
        else None,
        time_dt=event.get("clickTime"),
        osint=[
            OSINT(
                name=event.get("classification"),
                uid=event.get("threatID"),
                src_url=event.get("threatURL"),
                value=event.get("url"),
                type=OSINTIndicatorType.URL,
                email=Email(
                    to=to_list(event.get("recipient")),
                    from_=event.get("sender"),
                ),
            )
        ],
        app_name=event.get("userAgent"),
        action=ControlAction.DENIED if blocked else ControlAction.ALLOWED,
        disposition=Disposition.BLOCKED if blocked else Disposition.ALLOWED,
    )


def normalize_event(event: dict) -> Event:
    if "click" in event.get("type"):
        ioc_name = f"Suspicious URL click detected - {event.get('classification')}"
        ioc_external_id = event.get("classification")
        return Event(
            raw_event=event,
            event_timestamp=event.get("clickTime"),
            ioc=EventIOCInfo(
                external_id=ioc_external_id,
                external_name=ioc_name,
                has_ioc_definition=False,
                mitre_techniques=None,
            ),
            vendor_item_ref=None,
            vendor_group_ref=None,
            ocsf=convert_click_to_ocsf(event, event.get("type"), ioc_name),
        )

    if "message" in event.get("type"):
        threat_info_map = event.get("threatsInfoMap")

        classifications_set = set(t["classification"] for t in threat_info_map)
        threat_types_set = set(t["threatType"] for t in threat_info_map)
        sorted_classifications = sorted(classifications_set)
        sorted_threat_types = sorted(threat_types_set)

        ioc_name = f"Suspicious Email Detected - {'.'.join(sorted_classifications)} via {'.'.join(sorted_threat_types)}"
        ioc_external_id = ".".join(sorted_classifications + sorted_threat_types)
        return Event(
            raw_event=event,
            event_timestamp=event.get("messageTime"),
            ioc=EventIOCInfo(
                external_id=ioc_external_id,
                external_name=ioc_name,
                has_ioc_definition=False,
                mitre_techniques=None,
            ),
            vendor_item_ref=None,
            vendor_group_ref=None,
            ocsf=convert_message_to_ocsf(event, event.get("type"), ioc_name),
        )


class ProofpointV1EventSync(EventSync):
    @normalize(normalize_event)
    def execute(
        self,
        args: EventSyncArgs,
        bookmark: ProofpointV1EventSyncBookmark = None,
        **kwargs,
    ) -> Generator[Event, None, None]:
        api: ProofpointV1Api = self.integration.get_api()
        query_end_time = parse_datetime(bookmark.query_end_time)
        for key, value in api.get_all_siem_events(
            since_time=bookmark.query_end_time
        ).items():
            if key == "queryEndTime":
                dt = parse_datetime(value)
                query_end_time = dt
            else:
                for message in value:
                    message["type"] = key
                    yield message
        bookmark.query_end_time = format_datetime(query_end_time)

    def get_permission_checks(self):
        return []  # pragma: no cover
