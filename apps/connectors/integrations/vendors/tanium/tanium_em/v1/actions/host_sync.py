from typing import Generator

from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSyncArgs,
    OwnerAttributes,
)
from apps.connectors.integrations.actions.utils import (
    normalize_last_seen,
    parse_fqdn,
)
from apps.connectors.integrations.schemas import OsFamily
from apps.connectors.integrations.vendors.tanium.tanium_em.v1.actions import (
    HostSync,
    normalize,
)
from apps.connectors.integrations.vendors.tanium.tanium_em.v1.api import paginate
from apps.connectors.integrations.vendors.tanium.tanium_em.v1.health_check import (
    ReadAllHosts,
)

endpoint_platform_map = {
    "Windows": OsFamily.WINDOWS,
    "Mac": OsFamily.MAC,
    "Linux": OsFamily.LINUX,
}


def normalize_host(host_data: dict):
    hostname = host_data.get("name")
    hostname, domain = parse_fqdn(hostname)
    if domain is None:
        domain = host_data.get("domainName")
    ip_addresses = []
    mac_addresses = []
    if ip_address := host_data.get("ipAddress"):
        ip_addresses.append(ip_address)
    for ip_address in host_data.get("ipAddresses", []):
        if ip_address not in ip_addresses:
            ip_addresses.append(ip_address)
    mac_addresses.extend(host_data.get("macAddresses", []))

    os = host_data.get("os")
    os_name = ""
    if os:
        os_name = os.get("name") + endpoint_platform_map.get(os.get("platform"), "")

    owners = (
        [
            OwnerAttributes(
                name=primary_user.get("name", ""),
                email=primary_user.get("email"),
            )
        ]
        if (primary_user := host_data.get("primaryUser"))
        else []
    )
    last_seen = host_data.get("eidLastSeen")

    return Host(
        source_id=host_data.get("id"),
        hostname=hostname,
        _domain=domain,
        ip_addresses=ip_addresses,
        mac_addresses=mac_addresses,
        _os_name=os_name,
        owners=owners,
        last_seen=normalize_last_seen(last_seen),
        source_data=host_data,
    )


class TaniumEmV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_endpoints, **kwargs):
            yield from page

    def get_permission_checks(self):
        return [ReadAllHosts]
