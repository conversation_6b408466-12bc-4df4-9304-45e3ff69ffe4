from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig


class TaniumEmV1Config(TemplateVersionConfig):
    url: str = Field(
        title="Tanium Server",
        description="Tanium server to use.",
    )
    api_token: str = Field(
        title="API Token",
        description="Tanium API Token",
    )


class TaniumEmV1Connection(ConnectionTemplate):
    id = "tanium_em"
    name = "Tanium EM"
    config_model = TaniumEmV1Config
