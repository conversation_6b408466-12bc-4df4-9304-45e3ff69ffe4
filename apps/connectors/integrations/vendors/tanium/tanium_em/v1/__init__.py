from apps.connectors.integrations import TemplateVersion

from .connection import TaniumEmV1Config, TaniumEmV1Connection
from .integration import TaniumEmV1Integration
from .settings import TaniumEmV1Settings


class TaniumEmV1TemplateVersion(TemplateVersion):
    integration = TaniumEmV1Integration
    id = "v1"
    name = "v1"
    config_model = TaniumEmV1Config
    connection_model = TaniumEmV1Connection
    settings_model = TaniumEmV1Settings
