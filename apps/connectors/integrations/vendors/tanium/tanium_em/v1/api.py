from apps.connectors.integrations import ApiBase


def get_endpoints_query(first=100, after_cursor=None):
    after = f', after: "{after_cursor}"' if after_cursor else ""
    return f"""
        query {{
          endpoints(first: {first}{after}) {{
            edges {{
              cursor
              node {{
                id
                name
                ip
                os
                status
                lastSeen
                mac
                hostname
                isVirtual
                platform
                subnets
                lastLoggedUser
                installedApplications
                complianceStatus
                vulnerabilityStatus
                remediationStatus
              }}
            }}
            pageInfo {{
              hasNextPage
              endCursor
            }}
          }}
        }}
        """


def paginate(bound_method, **kwargs):
    has_next_page = True
    after_cursor = None

    while has_next_page:
        response = bound_method(after_cursor=after_cursor, **kwargs)
        data = response["data"]
        endpoints = data["endpoints"]

        if len(endpoints["edges"]) <= 0:
            break

        nodes = [edge["node"] for edge in endpoints["edges"]]
        yield nodes

        page_info = endpoints["pageInfo"]
        has_next_page = page_info["hasNextPage"]
        after_cursor = page_info["endCursor"]


class TaniumEmV1Api(ApiBase):
    def __init__(self, url=None, api_token=None, **kwargs):
        static_headers = {
            "Content-Type": "application/json",
            "session": api_token,
        }
        super().__init__(base_url=url, static_headers=static_headers)

    def query(self, query):
        url_path = self.url("/plugin/products/gateway/graphql")
        return self.session.post(url_path, json={"query": query}).json()

    def get_endpoints(self, first=100, after_cursor=None):
        url_path = self.url("/plugin/products/gateway/graphql")
        query = get_endpoints_query(first=first, after_cursor=after_cursor)
        return self.session.post(url_path, json={"query": query}).json()
