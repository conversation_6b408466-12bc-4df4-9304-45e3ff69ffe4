from apps.connectors.integrations.template import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import TaniumEmV1TemplateVersion


class TaniumEmTemplate(Template):
    id = "tanium_em"
    name = "Tanium Endpoint Management"
    category = Template.Category.ASSET_SOURCE
    versions = {
        TaniumEmV1TemplateVersion.id: TaniumEmV1TemplateVersion(),
    }
    vendor = Vendors.TANIUM
