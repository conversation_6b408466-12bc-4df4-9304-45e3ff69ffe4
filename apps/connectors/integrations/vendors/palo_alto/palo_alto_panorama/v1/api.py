from datetime import datetime
from typing import Any, Dict, List

import xmltodict

from apps.connectors.integrations import ApiBase


class PaloAltoPanoramaV1Api(ApiBase):
    def __init__(
        self,
        base_url: str = None,
        username: str = None,
        password: str = None,
        api_key: str = None,
    ):
        self.base_url = base_url
        self.username = username
        self.password = password
        self.access_token = api_key

        super().__init__(
            base_url=self.base_url, static_headers={"Accept": "application/json"}
        )

    def get_devices(self):
        # https://live.paloaltonetworks.com/t5/automation-api-discussions/retrieve-device-list-and-vsys-names-using-pan-rest-api/m-p/15238#M384
        url = self.url(
            "api/?type=op&key="
            + self.access_token
            + "&cmd=<show><devices><all></all></devices></show>"
        )
        # https://docs.paloaltonetworks.com/pan-os/10-1/pan-os-panorama-api/pan-os-xml-api-use-cases/query-a-firewall-from-panorama-api
        response = self.session.post(url)
        response_json = xmltodict.parse(response.text)
        return response_json["response"]["result"]["devices"]["entry"]

    def tag_ip_address(self, ip_address: str, tag_value: str):
        url = self.url("api/?type=user-id&key=" + self.access_token)
        self.session.headers.update(
            {"Content-Type": "application/x-www-form-urlencoded"}
        )
        xml_payload = """<uid-message>
                            <type>update</type>
                            <payload>
                                <register>
                                <entry ip="IP_ADDRESS" persistent="1">
                                    <tag>
                                    <member timeout="0">TAG_VALUE</member>
                                    </tag>
                                </entry>
                                </register>
                            </payload>
                            </uid-message>
                    """
        xml_payload = xml_payload.replace("IP_ADDRESS", ip_address).replace(
            "TAG_VALUE", tag_value
        )
        response = self.session.post(url, data=xml_payload)
        response_json = xmltodict.parse(response.text)
        return (
            "register" in response_json["response"]["result"]["uid-response"]["payload"]
        )

    def untag_ip_address(self, ip_address: str, tag_value: str):
        url = self.url("api/?type=user-id&key=" + self.access_token)
        self.session.headers.update(
            {"Content-Type": "application/x-www-form-urlencoded"}
        )
        xml_payload = """<uid-message>
                            <type>update</type>
                            <payload>
                                <unregister>
                                <entry ip="IP_ADDRESS" persistent="1">
                                    <tag>
                                    <member timeout="0">TAG_VALUE</member>
                                    </tag>
                                </entry>
                                </unregister>
                            </payload>
                            </uid-message>
                    """
        xml_payload = xml_payload.replace("IP_ADDRESS", ip_address).replace(
            "TAG_VALUE", tag_value
        )
        response = self.session.post(url, data=xml_payload)
        response_json = xmltodict.parse(response.text)
        return (
            "unregister"
            in response_json["response"]["result"]["uid-response"]["payload"]
        )

    def get_threat_logs(self, start_time: datetime) -> List[Dict[str, Any]]:
        """
        Retrieve threat logs from PanOS/Panorama using the XML API.

        Args:
            start_time: The datetime to start retrieving logs from

        Returns:
            List of threat log entries
        """
        # Format the date for the query
        formatted_date = start_time.strftime("%Y/%m/%d %H:%M:%S")

        # Create the query URL with the required parameters
        query_params = (
            f"type=log&log-type=threat&query=(receive_time geq '{formatted_date}')"
        )
        url = self.url(f"api/?{query_params}&key={self.access_token}")

        # Submit the log query job
        response = self.session.get(url)
        response_data = xmltodict.parse(response.text)

        # Check if the job was created successfully
        if response_data["response"]["@status"] != "success":
            raise Exception(f"Failed to create log query job: {response_data}")

        # Get the job ID
        job_id = response_data["response"]["result"]["job"]

        # Poll for job completion
        logs = self._poll_log_job(job_id)
        return logs

    def _poll_log_job(
        self, job_id: str, max_attempts: int = 30, poll_interval: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Poll for the completion of a log query job.

        Args:
            job_id: The ID of the job to poll
            max_attempts: Maximum number of polling attempts
            poll_interval: Time in seconds between polling attempts

        Returns:
            List of log entries
        """
        url = self.url(
            f"api/?type=log&action=get&job-id={job_id}&key={self.access_token}"
        )

        for _ in range(max_attempts):
            response = self.session.get(url)
            response_data = xmltodict.parse(response.text)

            status = response_data["response"]["@status"]
            if status != "success":
                raise Exception(f"Error checking job status: {response_data}")

            job_status = response_data["response"]["result"]["job"]["status"]

            if job_status == "FIN":
                # Job completed, retrieve the logs
                if "log" in response_data["response"]["result"]:
                    logs = response_data["response"]["result"]["log"]
                    # Handle the nested structure: log -> logs -> entry
                    if isinstance(logs, dict) and "logs" in logs:
                        logs_container = logs["logs"]
                        if (
                            isinstance(logs_container, dict)
                            and "entry" in logs_container
                        ):
                            entries = logs_container["entry"]
                            if isinstance(entries, list):
                                return entries
                            else:
                                return [entries]
                return []  # No logs found

            else:
                # Job failed or in an unknown state
                raise Exception(f"Log query job failed with status: {job_status}")
