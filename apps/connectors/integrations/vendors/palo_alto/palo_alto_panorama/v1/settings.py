from pydantic import ConfigDict, Field

from apps.connectors.integrations import IntegrationActionType, create_settings_model
from apps.connectors.integrations.template import TemplateVersionActionSettings


class PaloAltoPanoramaV1DAGSettings(TemplateVersionActionSettings):
    model_config = ConfigDict(title="Palo Alto Panorama Dynamic Address Group Settings")

    dynamic_address_group: str = Field(
        title="Dynamic Address Group",
        description="The name of the Dynamic Address Group for blocking IP addresses.",
    )


PaloAltoPanoramaV1Settings = create_settings_model(
    "PaloAltoPanoramaV1Settings",
    {
        IntegrationActionType.BLOCK_IP_FIREWALL: PaloAltoPanoramaV1DAGSettings,
        IntegrationActionType.UNBLOCK_IP_FIREWALL: PaloAltoPanoramaV1DAGSettings,
    },
)
