from datetime import datetime
from http import HTTPStatus

import responses

from apps.connectors.health_checks.component import (
    HealthCheckComponent,
    HealthCheckRequirement,
    RequirementStatus,
    ValidationStatus,
)
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.actions.event_sync import EventSyncArgs
from apps.connectors.integrations.actions.external_user_profile_link import (
    ExternalUserProfileLinkResult,
)
from apps.connectors.integrations.actions.user.get_sign_in_logs import (
    SignInLogsByIpArgs,
    SignInLogsByUserIdArgs,
)
from apps.connectors.integrations.health_check import IntegrationHealthCheckResult
from apps.connectors.integrations.schemas import UserIdentifierArgs
from apps.connectors.integrations.schemas.identifiers import (
    IpAddressIdentifier,
    UserIdentifier,
)
from apps.connectors.integrations.schemas.ocsf import (
    AuthenticationActivity,
    EndpointType,
    UserType,
)
from apps.connectors.integrations.vendors.okta.okta.v1.health_check import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    OktaUsersManage,
)
from apps.connectors.tests.integrations.base import (
    BaseIntegrationTest,
    HealthCheckComponentTestMixin,
)
from apps.demo.integrations.okta.v1.api import DemoOktaV1Api
from apps.tests.base import BaseTestCase
from factories.connector import ConnectorFactory


def setup_responses(user_id):
    responses.add(
        responses.GET,
        f"https://test_org_url.com/api/v1/users/{user_id}",
        json={
            "id": user_id,
            "status": "ACTIVE",
            "created": "2024-01-24T14:15:22Z",
            "lastUpdated": "2024-07-21T14:15:22Z",
            "lastLogin": "2024-08-13T15:58:20.353Z",
            "passwordChanged": "2024-08-13T15:58:20.353Z",
            "profile": {
                "firstName": "John",
                "lastName": "Doe",
                "displayName": "John Doe",
            },
        },
    )
    responses.add(
        responses.POST,
        f"https://test_org_url.com/api/v1/users/{user_id}/lifecycle/unsuspend",
        json={},
        status=HTTPStatus.OK,
    )
    responses.add(
        responses.POST,
        f"https://test_org_url.com/api/v1/users/{user_id}/lifecycle/suspend",
        json={},
        status=HTTPStatus.OK,
    )
    responses.add(
        responses.POST,
        f"https://test_org_url.com/api/v1/users/{user_id}/lifecycle/reset_password",
        json={
            "summary": "Reset password with email",
            "resetPasswordUrl": "https://test_org_url.com/reset_password/XE6wE17zmphl3KqAPFxO",
        },
        status=HTTPStatus.OK,
    )
    responses.add(
        responses.DELETE,
        f"https://test_org_url.com/api/v1/users/{user_id}/sessions",
        status=HTTPStatus.NO_CONTENT,
    )
    responses.add(
        responses.GET,
        "https://test_org_url.com/api/v1/logs",
        json=[
            {
                "actor": {
                    "id": "00uttidj01jqL21aM1d6",
                    "type": "User",
                    "alternateId": "<EMAIL>",
                    "displayName": "John Doe",
                    "detailEntry": None,
                },
                "client": {
                    "userAgent": {
                        "rawUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                        "os": "Mac OS X",
                        "browser": "CHROME",
                    },
                    "zone": None,
                    "device": "Computer",
                    "id": None,
                    "ipAddress": "********",
                    "geographicalContext": {
                        "city": "New York",
                        "state": "New York",
                        "country": "United States",
                        "postalCode": "10013",
                        "geolocation": {"lat": 40.3157, "lon": -74.01},
                    },
                },
                "device": {
                    "id": "guofdhyjex1feOgbN1d9",
                    "name": "Mac15,6",
                    "os_platform": "OSX",
                    "os_version": "14.6.0",
                    "managed": False,
                    "registered": True,
                    "device_integrator": None,
                    "disk_encryption_type": "ALL_INTERNAL_VOLUMES",
                    "screen_lock_type": "BIOMETRIC",
                    "jailbreak": None,
                    "secure_hardware_present": True,
                },
                "authenticationContext": {
                    "authenticationProvider": None,
                    "credentialProvider": None,
                    "credentialType": None,
                    "issuer": None,
                    "interface": None,
                    "authenticationStep": 0,
                    "rootSessionId": "idxBager62CSveUkTxvgRtonA",
                    "externalSessionId": "idxBager62CSveUkTxvgRtonA",
                },
                "displayMessage": "User login to Okta",
                "eventType": "user.session.start",
                "outcome": {"result": "SUCCESS", "reason": None},
                "published": "2024-08-13T15:58:20.353Z",
                "securityContext": {
                    "asNumber": 394089,
                    "asOrg": "ASN 0000",
                    "isp": "google",
                    "domain": None,
                    "isProxy": False,
                },
                "severity": "INFO",
                "debugContext": {
                    "debugData": {
                        "requestId": "ab609228fe84ce59cdcbfa690bcce016",
                        "requestUri": "/idp/idx/authenticators/poll",
                        "url": "/idp/idx/authenticators/poll",
                    }
                },
                "legacyEventType": "core.user_auth.login_success",
                "transaction": {
                    "type": "WEB",
                    "id": "ab609228fe84ce59cdcbfa690bgce016",
                    "detail": None,
                },
                "uuid": "dc9fd3c0-598c-11ef-8478-2b7584bf8d5a",
                "version": "0",
                "request": {
                    "ipChain": [
                        {
                            "ip": "********",
                            "geographicalContext": {
                                "city": "New York",
                                "state": "New York",
                                "country": "United States",
                                "postalCode": "10013",
                                "geolocation": {"lat": 40.3157, "lon": -74.01},
                            },
                            "version": "V4",
                            "source": None,
                        }
                    ]
                },
                "target": [
                    {
                        "id": "pfdfdhyjf0HMbkP2e1d7",
                        "type": "AuthenticatorEnrollment",
                        "alternateId": "unknown",
                        "displayName": "Okta Verify",
                        "detailEntry": None,
                    },
                    {
                        "id": "0oatxlef9sQvvqInq5d6",
                        "type": "AppInstance",
                        "alternateId": "Okta Admin Console",
                        "displayName": "Okta Admin Console",
                        "detailEntry": None,
                    },
                ],
            }
        ],
        status=HTTPStatus.OK,
    )


def setup_user_roles_responses(user_id, fail=False):
    responses.add(
        responses.GET,
        f"https://test_org_url.com/api/v1/users/{user_id}/roles?expand=targets/groups",
        json=[
            {
                "id": user_id,
                "type": "SUPER_ADMIN",
                "label": "Super Administrator" if not fail else "App Administrator",
                "status": "ACTIVE",
                "assignmentType": "USER",
                "_links": {
                    "assignee": {
                        "href": f"https://test_org_url.com/api/v1/users/{user_id}",
                    }
                },
            }
        ],
        status=HTTPStatus.OK,
    )
    responses.add(
        responses.GET,
        f"https://test_org_url.com/api/v1/users/me?expand=blocks",
        json={
            "id": user_id,
            "status": "ACTIVE",
            "created": "2024-01-24T14:15:22Z",
            "lastUpdated": "2024-07-21T14:15:22Z",
            "lastLogin": "2024-08-13T15:58:20.353Z",
            "passwordChanged": "2024-08-13T15:58:20.353Z",
        },
        status=HTTPStatus.OK,
    )


def setup_connection_data(fail=False):
    response = {
        "errorCode": "E0000006",
        "errorSummary": "You do not have permission to perform the requested action",
        "errorLink": "E0000006",
        "errorId": "sampleNUSD_8fdkFd8fs8SDBK",
        "errorCauses": [],
    }
    if not fail:
        response = {
            "address1": "100 1st St",
            "address2": "6th floor",
            "city": "San Fransico",
            "companyName": "okta",
            "country": "United States",
            "endUserSupportHelpURL": "support.okta.com",
            "phoneNumber": "+18887227871",
            "postalCode": "94105",
            "state": "California",
            "supportPhoneNumber": "+18887227871",
            "website": "www.okta.com",
            "id": "00o3qqiw0vSCIwu8I0g7",
            "created": "2024-01-24T14:15:22Z",
            "lastUpdated": "2024-07-21T14:15:22Z",
            "expiresAt": "2024-12-24T14:15:22Z",
            "status": "ACTIVE",
            "subdomain": "okta",
            "_links": {
                "preferences": {"href": "https://test_org_url.com/v1/org/preferences"},
                "uploadLogo": {
                    "href": "https://test_org_url.com/api/v1/org/logo",
                    "hints": {"allow": ["POST"]},
                },
                "oktaCommunication": {
                    "href": "https://test_org_url.com/api/v1/org/privacy/oktaCommunication"
                },
                "logo": None,
                "oktaSupport": {
                    "href": "https://test_org_url.com/api/v1/org/privacy/oktaSupport"
                },
                "contacts": {
                    "href": "https://vantest.oktapreview.com/api/v1/org/contacts"
                },
            },
        }
    responses.add(
        responses.GET,
        "https://test_org_url.com/api/v1/org",
        json=response,
        status=HTTPStatus.FORBIDDEN if fail else HTTPStatus.OK,
    )


def setup_security_event_responses():
    """Setup mock responses for security events following CrowdStrike test patterns."""
    responses.add(
        responses.GET,
        "https://test_org_url.com/api/v1/logs",
        json=[
            {
                "actor": {
                    "id": "00uttidj01jqL21aM1d6",
                    "type": "User",
                    "alternateId": "<EMAIL>",
                    "displayName": "John Doe",
                },
                "client": {
                    "userAgent": {
                        "rawUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                        "os": "Windows",
                        "browser": "CHROME",
                    },
                    "device": "Computer",
                    "ipAddress": "*************",
                    "geographicalContext": {
                        "city": "San Francisco",
                        "state": "California",
                        "country": "United States",
                        "postalCode": "94105",
                        "geolocation": {"lat": 37.7749, "lon": -122.4194},
                    },
                },
                "authenticationContext": {
                    "rootSessionId": "idx123456789",
                    "externalSessionId": "ext123456789",
                    "issuer": "https://example.okta.com",
                    "credentialProvider": "OKTA_CREDENTIAL_PROVIDER",
                    "credentialType": "OTP",
                    "interface": "IWA",
                    "authenticationStep": 0,
                },
                "displayMessage": "Malicious IP request detected",
                "eventType": "security.threat.detected",
                "outcome": {
                    "result": "FAILURE",
                    "reason": "Suspicious activity detected from known malicious IP",
                },
                "published": "2024-08-13T16:30:00.000Z",
                "severity": "WARN",
                "uuid": "security-event-123",
                "version": "0",
                "target": [
                    {
                        "id": "00uttidj01jqL21aM1d6",
                        "type": "User",
                        "alternateId": "<EMAIL>",
                        "displayName": "John Doe",
                    }
                ],
                "debugContext": {
                    "debugData": {
                        "requestId": "req123456789",
                        "threatSuspected": True,
                        "risk": {
                            "level": "HIGH",
                            "reasons": ["Malicious IP", "Anomalous Geo-Location"],
                        },
                    }
                },
            },
            {
                "actor": {
                    "id": "system",
                    "type": "SystemPrincipal",
                    "alternateId": "system",
                    "displayName": "System",
                },
                "client": {
                    "device": "Unknown",
                    "ipAddress": "*********",
                },
                "displayMessage": "Organization under attack detected",
                "eventType": "security.attack.start",
                "outcome": {"result": "FAILURE"},
                "published": "2024-08-13T16:35:00.000Z",
                "severity": "ERROR",
                "uuid": "security-event-456",
                "version": "0",
            },
            {
                "actor": {
                    "id": "00uttidj01jqL21aM1d6",
                    "type": "User",
                    "alternateId": "<EMAIL>",
                    "displayName": "Jane Smith",
                },
                "client": {
                    "device": "Mobile",
                    "ipAddress": "************",
                    "geographicalContext": {
                        "city": "New York",
                        "state": "New York",
                        "country": "United States",
                        "postalCode": "10001",
                        "geolocation": {"lat": 40.7128, "lon": -74.0060},
                    },
                },
                "displayMessage": "User risk level changed",
                "eventType": "user.risk.change",
                "outcome": {"result": "SUCCESS"},
                "published": "2024-08-13T16:40:00.000Z",
                "severity": "INFO",
                "uuid": "risk-event-789",
                "version": "0",
                "debugContext": {
                    "debugData": {
                        "risk": {
                            "level": "MEDIUM",
                            "previousLevel": "LOW",
                            "detectionName": "Anomalous Login Pattern",
                            "reasons": ["New Device", "New Location"],
                            "issuer": "OKTA",
                        },
                        "traceId": "trace-123-456-789",
                    }
                },
            },
        ],
        status=HTTPStatus.OK,
    )


def setup_security_event_responses_with_pagination():
    """Setup mock responses for testing pagination."""
    # First page
    responses.add(
        responses.GET,
        "https://test_org_url.com/api/v1/logs",
        json=[
            {
                "actor": {
                    "id": "user1",
                    "type": "User",
                    "alternateId": "<EMAIL>",
                },
                "eventType": "security.threat.detected",
                "published": "2024-08-13T16:30:00.000Z",
                "uuid": "event-1",
                "displayMessage": "Event 1",
                "outcome": {"result": "FAILURE"},
                "severity": "WARN",
            }
        ],
        headers={
            "Link": '<https://test_org_url.com/api/v1/logs?after=cursor123>; rel="next"'
        },
        status=HTTPStatus.OK,
    )

    # Second page
    responses.add(
        responses.GET,
        "https://test_org_url.com/api/v1/logs",
        json=[
            {
                "actor": {
                    "id": "user2",
                    "type": "User",
                    "alternateId": "<EMAIL>",
                },
                "eventType": "user.risk.detect",
                "published": "2024-08-13T16:35:00.000Z",
                "uuid": "event-2",
                "displayMessage": "Event 2",
                "outcome": {"result": "SUCCESS"},
                "severity": "INFO",
            }
        ],
        status=HTTPStatus.OK,
    )


def setup_error_response():
    """Setup mock error response for testing error handling."""
    responses.add(
        responses.GET,
        "https://test_org_url.com/api/v1/logs",
        json={"error": "Forbidden", "error_description": "Insufficient permissions"},
        status=HTTPStatus.FORBIDDEN,
    )


class OktaV1ApiTest(BaseTestCase):
    def setUp(self):
        super().setUp()

        integration = ConnectorFactory.get_integration(
            technology_id="okta",
        )
        self.api = integration.get_api()
        self.user_id = "test_user_id"

    @responses.activate
    def test_enable_user_login(self):
        setup_responses(self.user_id)
        self.api.enable_user_login(user_id=self.user_id)

    @responses.activate
    def test_disable_user_login(self):
        setup_responses(self.user_id)
        self.api.disable_user_login(user_id=self.user_id)

    @responses.activate
    def test_get_user(self):
        setup_responses(self.user_id)
        self.api.get_user(user_id=self.user_id)

    @responses.activate
    def test_reset_user_password(self):
        setup_responses(self.user_id)

        params = {"sendEmail": True}

        response = self.api.reset_user_password(self.user_id, params=params)
        self.assertEqual(response["summary"], "Reset password with email")
        self.assertEqual(
            response["resetPasswordUrl"],
            "https://test_org_url.com/reset_password/XE6wE17zmphl3KqAPFxO",
        )

    @responses.activate
    def test_revoke_user_sessions(self):
        setup_responses(self.user_id)

        self.api.revoke_user_sessions(self.user_id)

    @responses.activate
    def test_get_sign_in_logs(self):
        setup_responses(self.user_id)
        params = {
            "since": "2023-01-01T00:00:00Z",
            "until": "2023-01-02T00:00:00Z",
            "filter": f"'actor.id eq '{self.user_id}'",
        }

        response = self.api.get_sign_in_logs(params=params)
        self.assertEqual(response[0]["actor"]["id"], "00uttidj01jqL21aM1d6")


class OktaV1IntegrationTest(BaseIntegrationTest):
    def setUp(self):
        super().setUp()
        self.integration = ConnectorFactory.get_integration(
            technology_id="okta",
        )

        self.user_id = "test_user_id"

    @responses.activate
    def test_get_user_info(self):
        setup_responses(self.user_id)

        user_id = UserIdentifier(value=self.user_id, value_type="okta")
        args = UserIdentifierArgs(user_id=user_id)

        response = self.integration.invoke_action(
            IntegrationActionType.GET_USER_INFO,
            args,
        )
        user = response.result
        self.assertEqual(user.uid, self.user_id)
        self.assertEqual(user.display_name, "John Doe")
        self.assertTrue(user.is_enabled)

    @responses.activate
    def test_enable_user_login(self):
        setup_responses(self.user_id)

        user_id = UserIdentifier(value=self.user_id, value_type="okta")
        args = UserIdentifierArgs(user_id=user_id)

        response = self.integration.invoke_action(
            IntegrationActionType.ENABLE_USER_LOGIN,
            args,
        )
        self.assertTrue(response.result.enabled)

    @responses.activate
    def test_disable_user_login(self):
        setup_responses(self.user_id)

        user_id = UserIdentifier(value=self.user_id, value_type="okta")
        args = UserIdentifierArgs(user_id=user_id)

        response = self.integration.invoke_action(
            IntegrationActionType.DISABLE_USER_LOGIN,
            args,
        )
        self.assertFalse(response.result.enabled)

    @responses.activate
    def test_reset_user_password(self):
        setup_responses(self.user_id)

        user_id = UserIdentifier(value=self.user_id, value_type="okta")
        args = UserIdentifierArgs(user_id=user_id)

        response = self.integration.invoke_action(
            IntegrationActionType.RESET_USER_PASSWORD,
            args,
        )
        self.assertTrue(response.result.reset)

    @responses.activate
    def test_revoke_user_sessions(self):
        setup_responses(self.user_id)

        user_id = UserIdentifier(value=self.user_id, value_type="okta")
        args = UserIdentifierArgs(user_id=user_id)

        response = self.integration.invoke_action(
            IntegrationActionType.REVOKE_USER_SESSIONS,
            args,
        )

        self.assertTrue(response.result.revoked)

    def test_get_external_user_profile_link_action(self):
        self.setup_integration()
        user_id = UserIdentifier(value=self.user_id, value_type="okta")
        args = UserIdentifierArgs(user_id=user_id)

        result = self.integration.invoke_action(
            IntegrationActionType.GET_EXTERNAL_USER_PROFILE_LINK,
            args,
        )

        self.assertTrue(isinstance(result, ExternalUserProfileLinkResult))
        self.assertEqual(
            result.template,
            f"https://test_org_url-admin.com/admin/user/profile/view/{self.user_id}",
        )

    @responses.activate
    def test_get_sign_in_logs_by_user(self):
        setup_responses(self.user_id)

        args = SignInLogsByUserIdArgs(
            user_id=UserIdentifier(value=self.user_id, value_type="okta"),
            start_time="2023-01-01T00:00:00Z",
            end_time="2023-01-02T00:00:00Z",
        )

        response = self.integration.invoke_action(
            IntegrationActionType.GET_SIGN_IN_LOGS_BY_USER_ID,
            args,
        )
        result = response.result[0]
        self.assert_sign_in(result)

    @responses.activate
    def test_get_sign_in_logs_by_ip(self):
        setup_responses(self.user_id)

        args = SignInLogsByIpArgs(
            ip_address=IpAddressIdentifier(value="********"),
            start_time="2023-01-01T00:00:00Z",
            end_time="2023-01-02T00:00:00Z",
        )

        response = self.integration.invoke_action(
            IntegrationActionType.GET_SIGN_IN_LOGS_BY_IP,
            args,
        )
        result = response.result[0]
        self.assert_sign_in(result)

    @responses.activate
    def test_event_sync(self):
        """Test event sync functionality for security events following CrowdStrike patterns."""
        setup_security_event_responses()

        # Get bookmark and set initial time
        bookmark = self.integration.get_bookmark(IntegrationActionType.EVENT_SYNC)
        self.assertIsNotNone(bookmark)
        bookmark.latest_event_published_datetime = "2024-08-13T16:00:00.000Z"

        args = EventSyncArgs()

        response = self.integration.invoke_action(
            IntegrationActionType.EVENT_SYNC,
            action_args=args,
            bookmark=bookmark,
        )

        # Should return a list of normalized events
        events = list(response.result)
        self.assertEqual(len(events), 3)  # Updated to match new test data

        # Test first event (security.threat.detected)
        threat_event = events[0]
        self.assertEqual(threat_event.class_name, "Authentication")
        self.assertEqual(threat_event.class_uid, 3002)
        self.assertEqual(threat_event.category_name, "Identity & Access Management")
        self.assertEqual(threat_event.category_uid, 3)
        self.assertEqual(threat_event.severity, "Medium")  # WARN maps to Medium
        self.assertEqual(threat_event.severity_id, 3)
        self.assertEqual(threat_event.status_id, 2)  # FAILURE maps to Failure
        self.assertEqual(threat_event.metadata.product.name, "Okta System Log")
        self.assertEqual(threat_event.metadata.product.vendor_name, "Okta")
        self.assertEqual(threat_event.metadata.version, "1.5.0")

        # Test actor mapping (User type should be populated)
        self.assertEqual(threat_event.actor.uid, "00uttidj01jqL21aM1d6")
        self.assertEqual(threat_event.actor.name, "<EMAIL>")
        self.assertEqual(threat_event.actor.full_name, "John Doe")
        self.assertEqual(threat_event.actor.type, UserType.USER)

        # Test source endpoint mapping
        self.assertEqual(threat_event.src_endpoint.ip, "*************")
        self.assertEqual(threat_event.src_endpoint.location.city, "San Francisco")
        self.assertEqual(threat_event.src_endpoint.location.region, "California")
        self.assertEqual(
            threat_event.src_endpoint.type_id, EndpointType.DESKTOP
        )  # Computer maps to Desktop

        # Test target user mapping
        self.assertEqual(threat_event.user.uid, "00uttidj01jqL21aM1d6")
        self.assertEqual(threat_event.user.name, "<EMAIL>")

        # Test session mapping with ITP context
        self.assertEqual(threat_event.session.uid, "idx123456789")
        self.assertEqual(threat_event.session.issuer, "https://example.okta.com")
        self.assertIsNotNone(threat_event.session.unmapped)
        self.assertEqual(
            threat_event.session.unmapped["external_session_id"], "ext123456789"
        )
        self.assertEqual(
            threat_event.session.unmapped["credential_provider"],
            "OKTA_CREDENTIAL_PROVIDER",
        )

        # Test message preference for security.threat.detected
        self.assertEqual(
            threat_event.message, "Suspicious activity detected from known malicious IP"
        )

        # Test second event (security.attack.start)
        attack_event = events[1]
        self.assertEqual(attack_event.severity, "High")  # ERROR maps to High
        self.assertEqual(
            attack_event.actor.type, UserType.SYSTEM
        )  # SystemPrincipal maps to System
        self.assertEqual(attack_event.activity_id, AuthenticationActivity.LOGON)

        # Test third event (user.risk.change)
        risk_event = events[2]
        self.assertEqual(
            risk_event.severity, "Informational"
        )  # INFO maps to Informational
        self.assertEqual(risk_event.actor.type, UserType.USER)
        self.assertEqual(risk_event.activity_id, AuthenticationActivity.OTHER)
        self.assertEqual(
            risk_event.src_endpoint.type_id, EndpointType.MOBILE
        )  # Mobile device

        # Test bookmark update
        self.assertEqual(
            bookmark.latest_event_published_datetime, "2024-08-13T16:40:00.000Z"
        )

    @responses.activate
    def test_event_sync_pagination(self):
        """Test event sync with pagination following CrowdStrike patterns."""
        setup_security_event_responses_with_pagination()

        bookmark = self.integration.get_bookmark(IntegrationActionType.EVENT_SYNC)
        bookmark.latest_event_published_datetime = "2024-08-13T16:00:00.000Z"

        args = EventSyncArgs()

        response = self.integration.invoke_action(
            IntegrationActionType.EVENT_SYNC,
            action_args=args,
            bookmark=bookmark,
        )

        events = list(response.result)
        self.assertEqual(len(events), 2)  # Should get events from both pages

        # Verify events from different pages
        self.assertEqual(events[0].raw_event["uuid"], "event-1")
        self.assertEqual(events[1].raw_event["uuid"], "event-2")

        # Test bookmark update to latest event
        self.assertEqual(
            bookmark.latest_event_published_datetime, "2024-08-13T16:35:00.000Z"
        )

    @responses.activate
    def test_event_sync_error_handling(self):
        """Test event sync error handling."""
        setup_error_response()

        bookmark = self.integration.get_bookmark(IntegrationActionType.EVENT_SYNC)
        args = EventSyncArgs()

        with self.assertRaises(Exception):
            response = self.integration.invoke_action(
                IntegrationActionType.EVENT_SYNC,
                action_args=args,
                bookmark=bookmark,
            )
            list(response.result)  # Force evaluation of generator

    def test_event_sync_bookmarks(self):
        """Test event sync bookmark functionality."""
        from apps.connectors.integrations.vendors.okta.okta.v1.bookmarks import (
            OktaV1Bookmarks,
            OktaV1EventSyncBookmark,
        )

        bookmark = OktaV1EventSyncBookmark()
        self.assertIsNotNone(bookmark.latest_event_published_datetime)

        # Test bookmark schema
        schema = OktaV1Bookmarks.model_json_schema()
        self.assertIn(IntegrationActionType.EVENT_SYNC.value, schema["properties"])

        bookmark_schema = schema["properties"][IntegrationActionType.EVENT_SYNC.value]
        self.assertIn("latest_event_published_datetime", bookmark_schema["properties"])

    def assert_sign_in(self, result):
        self.assertEqual(result.class_name, "Authentication")
        self.assertEqual(result.class_uid, 3002)
        self.assertEqual(result.category_name, "Identity & Access Management")
        self.assertEqual(result.category_uid, 3)
        self.assertEqual(result.severity, "Informational")
        self.assertEqual(result.severity_id, 1)
        self.assertEqual(result.metadata.product.name, "Okta System Log")
        self.assertEqual(result.metadata.product.vendor_name, "Okta")
        self.assertEqual(result.metadata.profiles, ["datetime", "host"])
        self.assertEqual(result.actor.user.name, "John Doe")
        self.assertEqual(result.actor.user.email_addr, "<EMAIL>")
        self.assertEqual(result.actor.user.uid, "00uttidj01jqL21aM1d6")
        self.assertEqual(result.actor.session.uid, "idxBager62CSveUkTxvgRtonA")
        self.assertEqual(result.actor.session.issuer, None)
        self.assertEqual(result.src_endpoint.type, "Computer")
        self.assertEqual(result.src_endpoint.location.city, "New York")
        self.assertEqual(result.src_endpoint.location.region, "New York")
        self.assertEqual(result.src_endpoint.location.country, "United States")
        self.assertEqual(result.src_endpoint.location.postal_code, "10013")
        self.assertEqual(result.src_endpoint.location.lat, 40.3157)
        self.assertEqual(result.src_endpoint.location.long, -74.01)
        self.assertEqual(result.src_endpoint.ip, "********")
        self.assertEqual(
            result.http_request.user_agent,
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        )
        self.assertEqual(result.device.uid, "guofdhyjex1feOgbN1d9")
        self.assertEqual(result.device.name, "Mac15,6")
        self.assertEqual(result.device.is_managed, False)
        self.assertEqual(result.dst_endpoint.svc_name, "/idp/idx/authenticators/poll")
        self.assertEqual(result.http_request.uid, "ab609228fe84ce59cdcbfa690bcce016")
        self.assertEqual(result.message, "User login to Okta")
        self.assertEqual(result.metadata.event_code, "user.session.start")
        self.assertEqual(result.status, "Success")
        self.assertEqual(result.status_id, 1)
        self.assertEqual(result.status_code, "SUCCESS")
        self.assertEqual(
            result.time_dt, datetime.fromisoformat("2024-08-13T15:58:20.353+00:00")
        )
        self.assertEqual(result.src_endpoint.autonomous_system.number, 394089)
        self.assertEqual(result.src_endpoint.autonomous_system.name, "ASN 0000")
        self.assertEqual(result.src_endpoint.domain, None)
        self.assertEqual(result.src_endpoint.location.isp, "google")
        self.assertEqual(result.metadata.uid, "dc9fd3c0-598c-11ef-8478-2b7584bf8d5a")
        self.assertEqual(result.metadata.product.version, "0")


class OktaV1HealthCheckTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.integration = ConnectorFactory.get_integration(
            technology_id="okta",
        )

    @responses.activate
    def test_health_check(self):
        setup_connection_data(fail=False)
        health_check = ConnectionHealthCheck(
            integration=self.integration,
        )
        self.assertEqual(
            health_check.get_result(),
            IntegrationHealthCheckResult.PASSED,
        )

    @responses.activate
    def test_health_check_fail(self):
        setup_connection_data(fail=True)
        health_check = ConnectionHealthCheck(
            integration=self.integration,
        )
        self.assertEqual(
            health_check.get_result(),
            IntegrationHealthCheckResult.FAILED,
        )

    @responses.activate
    def test_health_check_with_error(self):
        responses.get(
            f"https://test_org_url.com/api/v1/users/me?expand=blocks",
            json={
                "errorCode": "E0000006",
                "errorSummary": "You do not have permission to perform the requested action",
                "errorLink": "E0000006",
                "errorId": "sampleNUSD_8fdkFd8fs8SDBK",
            },
            status=HTTPStatus.FORBIDDEN,
        )
        health_check = OktaUsersManage(integration=self.integration)
        self.assertEqual(
            health_check.get_result(),
            IntegrationHealthCheckResult.FAILED,
        )


class OktaV1HealthCheckComponentsTest(BaseTestCase, HealthCheckComponentTestMixin):
    def setUp(self):
        super().setUp()
        self._patch_encryption()
        self.connector = ConnectorFactory(
            technology_id="okta",
            enabled_actions=[
                IntegrationActionType.ENABLE_USER_LOGIN,
                IntegrationActionType.DISABLE_USER_LOGIN,
                IntegrationActionType.RESET_USER_PASSWORD,
                IntegrationActionType.REVOKE_USER_SESSIONS,
                IntegrationActionType.GET_SIGN_IN_LOGS_BY_IP,
                IntegrationActionType.GET_SIGN_IN_LOGS_BY_USER_ID,
                IntegrationActionType.GET_EXTERNAL_USER_PROFILE_LINK,
                IntegrationActionType.GET_USER_INFO,
            ],
        )

    @responses.activate
    def test_health_check_components_passed(self):
        setup_connection_data(fail=False)
        setup_user_roles_responses("test_user_id", fail=False)
        components = HealthCheckComponent.get_components(connector=self.connector)

        critical_checks_expected = [
            HealthCheckRequirement(
                name="Connection is valid",
                description="Can connect to the integration API",
                value=None,
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            )
        ]

        okta_users_manage = [
            HealthCheckRequirement(
                name="Okta Users Manage",
                description="Allows the app to manage users in Okta",
                value="Okta Users Manage",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            )
        ]
        okta_logs_read = [
            HealthCheckRequirement(
                name="Okta Logs Read",
                description="Allows the app to read logs in Okta",
                value="Okta Logs Read",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            )
        ]
        okta_users_read = [
            HealthCheckRequirement(
                name="Okta Users Read",
                description="Allows the app to read users in Okta",
                value="Okta Users Read",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            )
        ]

        self.assert_components(
            components,
            [
                critical_checks_expected,
                okta_users_manage,
                okta_users_manage,
                okta_users_read,
                okta_users_manage,
                okta_users_manage,
                okta_logs_read,
                okta_logs_read,
            ],
        )

    @responses.activate
    def test_health_check_components_failed(self):
        setup_connection_data(fail=False)
        setup_user_roles_responses("test_user_id", fail=True)
        components = HealthCheckComponent.get_components(connector=self.connector)

        critical_checks_expected = [
            HealthCheckRequirement(
                name="Connection is valid",
                description="Can connect to the integration API",
                value=None,
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            )
        ]

        okta_users_manage = [
            HealthCheckRequirement(
                name="Okta Users Manage",
                description="Allows the app to manage users in Okta",
                value="Okta Users Manage",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.FAILED,
            )
        ]
        okta_logs_read = [
            HealthCheckRequirement(
                name="Okta Logs Read",
                description="Allows the app to read logs in Okta",
                value="Okta Logs Read",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.FAILED,
            )
        ]
        okta_users_read = [
            HealthCheckRequirement(
                name="Okta Users Read",
                description="Allows the app to read users in Okta",
                value="Okta Users Read",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.FAILED,
            )
        ]

        self.assert_components(
            components,
            [
                critical_checks_expected,
                okta_users_manage,
                okta_users_manage,
                okta_users_read,
                okta_users_manage,
                okta_users_manage,
                okta_logs_read,
                okta_logs_read,
            ],
        )


class OktaDemoV1IntegrationTest(BaseTestCase):
    def test_demo_okta_v1_api_initialization(self):
        # Arrange
        test_organization_alias = "test_org"
        test_technology_id = "tech_123"

        # Act
        api = DemoOktaV1Api(
            organization_alias=test_organization_alias, technology_id=test_technology_id
        )

        # Assert
        self.assertEqual(api.organization_alias, test_organization_alias)
        self.assertEqual(api.technology_id, test_technology_id)
