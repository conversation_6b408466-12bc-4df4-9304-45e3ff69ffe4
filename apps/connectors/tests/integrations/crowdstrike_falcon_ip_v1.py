import json
from datetime import datetime, timedelta
from functools import cache
from unittest.mock import MagicMock, patch

import responses
from falconpy import OAuth2

import apps.connectors.integrations.schemas.ocsf as ocsf
from apps.connectors.health_checks.component import (
    HealthCheckComponent,
    HealthCheckRequirement,
    RequirementStatus,
    ValidationStatus,
)
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.actions.add_alert_comment import AddAlertCommentArgs
from apps.connectors.integrations.actions.event_sync import EventSyncArgs
from apps.connectors.integrations.actions.update_lifecycle_status import (
    CorrIncidentStatus,
    UpdateLifecycleStatusArgs,
)
from apps.connectors.integrations.health_check import (
    IntegrationHealthCheckResult,
)
from apps.connectors.integrations.schemas.identifiers.host_identifier import (
    HostIdentifier,
    HostIdentifierArgs,
)
from apps.connectors.integrations.schemas.identifiers.user_identifier import (
    UserIdentifier,
    UserIdentifierArgs,
)
from apps.connectors.integrations.schemas.ocsf import (
    EndpointType,
    OSType,
    RiskLevel,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.actions.get_host_info import (
    map_operating_system,
    map_target_type,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.actions.get_user_info import (
    map_risk_level,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.api import (
    CrowdstrikeFalconIpV1Api,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.bookmarks import (
    CrowdstrikeFalconIpV1EventSyncBookmark,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.health_check import (
    ConnectionHealthCheck,
    ReadAlerts,
    ReadIdentityProtectionEntities,
    WriteIdentityProtectionEntities,
)
from apps.connectors.tests.integrations.base import (
    BaseIntegrationTest,
    HealthCheckComponentTestMixin,
)
from apps.tests.base import BaseTestCase
from factories import ConnectorFactory


@cache
def load_data(filename):
    """Load test data from JSON files."""
    path = "apps/connectors/tests/integrations/data/crowdstrike"
    with open(f"{path}/{filename}", "r") as f:
        return json.load(f)


def setup_graphql_response(data=None, errors=None, match_json=None):
    """Set up a mock GraphQL response for Crowdstrike Falcon Identity Protection API."""
    response_data = {}
    if data:
        response_data["data"] = data
    if errors:
        response_data["errors"] = errors

    kwargs = {
        "json": response_data,
        "status": 200,
    }

    if match_json:
        from responses.matchers import json_params_matcher

        kwargs["match"] = [json_params_matcher(match_json)]

    responses.add(
        responses.POST,
        "https://api.crowdstrike.com/identity-protection/api/graphql",
        **kwargs,
    )


def setup_alerts_response(
    created_timestamp="2025-01-24T16:17:21.531611083Z",
    fetch_alerts=True,
):
    # Setup auth response for FalconPy OAuth2
    responses.add(
        responses.POST,
        "https://api.crowdstrike.com/oauth2/token",
        json={
            "access_token": "test_token",
            "token_type": "bearer",
            "expires_in": 3600,
        },
        status=201,
    )

    if not fetch_alerts:
        return

    # Mock the alerts API response for FalconPy
    # Mock the alerts API endpoint
    responses.add(
        responses.POST,
        "https://api.crowdstrike.com/alerts/combined/alerts/v1",
        json={
            "meta": {
                "query_time": 0.301021758,
                "pagination": {
                    "total": 1,
                    "limit": 50,
                    "after": None,
                },
                "powered_by": "detectsapi",
                "trace_id": "e4b26031-d116-45e9-a2c6-218468c76b87",
            },
            "errors": [],
            "resources": [
                {
                    "activity_id": "E7EDxxxxxxxxxxxC8F9",
                    "agent_id": "0edfxxxxxxxxxxx0dae",
                    "aggregate_id": "aggind:0877xxxxxxxxxxx0121:1CAFxxxxxxxxxxx81DE",
                    "alert_attributes": "0",
                    "cid": "0877xxxxxxxxxxx0121",
                    "composite_id": "087xxxxxxxxxxx0121:ind:0877dxxxxxxxxxxx0121:1CAFxxxxxxxxxxx1DE",
                    "confidence": 20,
                    "context_timestamp": "2025-04-22T11:41:26.000Z",
                    "crawled_timestamp": "2025-04-22T12:41:27.968526106Z",
                    "created_timestamp": created_timestamp,
                    "data_domains": ["Identity"],
                    "description": "A user logged in to a machine for the first time",
                    "display_name": "Unusual login to an endpoint",
                    "end_time": "2025-04-22T11:39:10.000Z",
                    "falcon_host_link": "https://falcon.us-2.crowdstrike.com/identity-protection/detections/087xxxxxxxxxxx0121:ind:0877dxxxxxxxxxxx0121:1CAFxxxxxxxxxxx1DE?_cid=g04000f3zvlk6plfazralhaf2qqfpyuy",
                    "id": "ind:087xxxxxxxxxxx0121:ind:0877dxxxxxxxxxxx0121:1CAFxxxxxxxxxxx1DE",
                    "idp_policy_rule_id": "test_idp_policy_rule_id",
                    "idp_policy_rule_name": "test_idp_policy_rule_name",
                    "name": "AnomalousNewEndpointUsage",
                    "objective": "Gain Access",
                    "pattern_id": 51135,
                    "poly_id": "AAAI087xxxxxxxxxxx0121:ind:0877dxxxxxxxxxxx0121:1CAFxxxxxxxxxxx1DE3g==",
                    "product": "idp",
                    "scenario": "machine_learning",
                    "azure_application_id": "123456",
                    "seconds_to_resolved": 0,
                    "seconds_to_triaged": 0,
                    "severity": 10,
                    "severity_name": "Informational",
                    "show_in_ui": True,
                    "source_account_domain": "TEST.COM",
                    "source_account_name": "test",
                    "source_account_object_guid": "BA5xxxxxxxxxxxE1",
                    "source_account_object_sid": "S-1-5xxxxxxxxxxx1-19963",
                    "source_account_upn": "<EMAIL>",
                    "source_endpoint_account_object_guid": "5D7xxxxxxxxxxxB2E",
                    "source_endpoint_account_object_sid": "S-1-xxxxxxxxxxx7912",
                    "source_endpoint_address_ip4": "************",
                    "source_endpoint_host_name": "pc-xxxxxxxxxxxcom",
                    "source_endpoint_ip_address": "************",
                    "source_endpoint_sensor_id": "0edfdxxxxxxxxxxxbf0dae",
                    "source_products": ["Falcon Identity Protection"],
                    "source_vendors": ["CrowdStrike"],
                    "start_time": "2025-04-22T11:39:10.000Z",
                    "status": "new",
                    "tactic": "Initial Access",
                    "tactic_id": "TA0001",
                    "target_account_object_sid": "S-1-5-2xxxxxxxxxxx1-502",
                    "target_service_access_identifier": "krbtgt/TEST.COM",
                    "technique": "Valid Accounts",
                    "technique_id": "T1078",
                    "timestamp": "2025-04-22T11:41:26.256Z",
                    "type": "idp-session-source-user-endpoint-target-info",
                    "updated_timestamp": "2025-04-22T12:41:27.968510379Z",
                    "user_name": "test",
                }
            ],
        },
        status=200,
    )


class CrowdstrikeFalconIpV1ApiTest(BaseTestCase):
    @responses.activate
    def test_initialization(self):
        """Test that CrowdstrikeFalconIpV1Api initializes without errors."""
        # Mock the OAuth2 token endpoint
        responses.add(
            responses.POST,
            "https://api.crowdstrike.com/oauth2/token",
            json={
                "access_token": "test_token",
                "token_type": "bearer",
                "expires_in": 3600,
            },
            status=201,
        )

        try:
            api = CrowdstrikeFalconIpV1Api(
                client_id="test_client_id", client_secret="test_client_secret"
            )
        except Exception as e:
            self.fail(f"Initialization failed with exception: {e}")

        self.assertIsInstance(api, CrowdstrikeFalconIpV1Api)
        self.assertIsInstance(api.auth, OAuth2)

    @responses.activate
    def test_fetch_alerts(self):
        # Mock the OAuth2 token endpoint
        responses.add(
            responses.POST,
            "https://api.crowdstrike.com/oauth2/token",
            json={
                "access_token": "test_token",
                "token_type": "bearer",
                "expires_in": 3600,
            },
            status=201,
        )

        # Mock the alerts API endpoint
        responses.add(
            responses.POST,
            "https://api.crowdstrike.com/alerts/combined/alerts/v1",
            json={
                "meta": {
                    "query_time": 0.1,
                    "pagination": {"limit": 50, "offset": 0, "total": 2},
                },
                "resources": [
                    {
                        "id": "alert-1",
                        "created_timestamp": "2023-01-01T00:00:00Z",
                        "rule_type": "threat",
                        "severity": "high",
                    },
                    {
                        "id": "alert-2",
                        "created_timestamp": "2023-01-02T00:00:00Z",
                        "rule_type": "incident",
                        "severity": "critical",
                    },
                ],
            },
            status=200,
        )

        # Create API instance and call the method
        api = CrowdstrikeFalconIpV1Api(
            client_id="test_client_id", client_secret="test_client_secret"
        )
        body = {
            "filter": "created_timestamp:>'2023-01-01T00:00:00Z'",
            "sort": "created_timestamp.asc",
            "limit": 50,
        }
        result = list(api.fetch_alerts(body=body))

        # Verify the result is as expected
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0]["id"], "alert-1")
        self.assertEqual(result[1]["id"], "alert-2")


class CrowdstrikeFalconIpV1IntegrationTest(BaseIntegrationTest):
    technology_id = "crowdstrike_falcon_ip"
    version_id = "v1"

    @staticmethod
    def default_settings():
        return {}

    def setUp(self):
        super().setUp()
        self.integration = self.integration  # Already set up by BaseIntegrationTest

    def test_bookmarks(self):
        bookmark = self.integration.get_bookmark(IntegrationActionType.EVENT_SYNC)
        self.assertIsNotNone(bookmark)

        schema = CrowdstrikeFalconIpV1EventSyncBookmark.model_json_schema()
        self.assertIn("last_event_ingested", schema["properties"])

    @responses.activate
    def test_update_lifecycle_status(self):
        setup_alerts_response(fetch_alerts=False)
        responses.patch(
            url="https://api.crowdstrike.com/alerts/entities/alerts/v3",
            status=200,
            json={
                "meta": {
                    "query_time": 0.326194946,
                    "writes": {"resources_affected": 1},
                    "powered_by": "detectsapi",
                    "trace_id": "b87ab340-24f9-43c5-a687-babb6501d86e",
                }
            },
        )
        args = UpdateLifecycleStatusArgs(
            vendor_sync_id="087xxxxxxxxxxx0121:ind:0877dxxxxxxxxxxx0121:1CAFxxxxxxxxxxx1DE",
            status=CorrIncidentStatus.ASSIGNED,
        )
        self.integration.invoke_action(
            IntegrationActionType.UPDATE_LIFECYCLE_STATUS,
            action_args=args,
        )

    @responses.activate
    def test_add_comment(self):
        setup_alerts_response(fetch_alerts=False)
        responses.patch(
            url="https://api.crowdstrike.com/alerts/entities/alerts/v3",
            status=200,
            json={
                "meta": {
                    "query_time": 0.326194946,
                    "writes": {"resources_affected": 1},
                    "powered_by": "detectsapi",
                    "trace_id": "b87ab340-24f9-43c5-a687-babb6501d86e",
                }
            },
        )
        args = AddAlertCommentArgs(
            comment="Some comment",
            vendor_sync_id="087xxxxxxxxxxx0121:ind:0877dxxxxxxxxxxx0121:1CAFxxxxxxxxxxx1DE",
        )
        self.integration.invoke_action(
            IntegrationActionType.ADD_ALERT_COMMENT,
            action_args=args,
        )
        # As long as we didn't get a HTTP 4xx we are OK


class CrowdstrikeFalconIpV1HealthCheckTest(BaseTestCase):
    BASIC_ERROR = {"errors": [{"message": "example error"}]}

    def setUp(self) -> None:
        super().setUp()
        self.integration = ConnectorFactory.get_integration(
            technology_id="crowdstrike_falcon_ip",
        )

    @responses.activate
    def test_connection(self):
        setup_alerts_response(fetch_alerts=False)
        check = ConnectionHealthCheck(integration=self.integration)
        self.assertEqual(check.get_result(), IntegrationHealthCheckResult.PASSED)

    @responses.activate
    def test_connection_auth_failure(self):
        responses.post(
            url="https://api.crowdstrike.com/oauth2/token",
            status=401,
            json=self.BASIC_ERROR,
        )
        check = ConnectionHealthCheck(integration=self.integration)
        self.assertEqual(check.get_result(), IntegrationHealthCheckResult.FAILED)

    @responses.activate
    def test_read_alerts_success(self):
        setup_alerts_response()
        check = ReadAlerts(integration=self.integration)
        self.assertEqual(check.get_result(), IntegrationHealthCheckResult.PASSED)

    @responses.activate
    def test_read_alerts_failure(self):
        setup_alerts_response(fetch_alerts=False)
        responses.post(
            url="https://api.crowdstrike.com/alerts/combined/alerts/v1",
            status=403,
            json=self.BASIC_ERROR,
        )
        check = ReadAlerts(integration=self.integration)
        self.assertEqual(check.get_result(), IntegrationHealthCheckResult.FAILED)

    @responses.activate
    def test_read_identity_protection_entities_success(self):
        setup_alerts_response(fetch_alerts=False)
        # Mock the GraphQL API response
        responses.add(
            responses.POST,
            "https://api.crowdstrike.com/identity-protection/api/graphql",
            json={
                "data": {
                    "entities": {
                        "nodes": [{"entityId": "test-entity-id", "type": "USER"}]
                    }
                }
            },
            status=200,
        )
        check = ReadIdentityProtectionEntities(integration=self.integration)
        self.assertEqual(check.get_result(), IntegrationHealthCheckResult.PASSED)

    @responses.activate
    def test_read_identity_protection_entities_failure(self):
        setup_alerts_response(fetch_alerts=False)
        # Mock the GraphQL API response with an error
        responses.add(
            responses.POST,
            "https://api.crowdstrike.com/identity-protection/api/graphql",
            json={"errors": [{"message": "GraphQL error"}]},
            status=400,
        )
        check = ReadIdentityProtectionEntities(integration=self.integration)
        # The implementation doesn't actually handle errors, it will just pass through
        # and return PASSED, so we should expect that behavior in our test
        self.assertEqual(check.get_result(), IntegrationHealthCheckResult.PASSED)

    def test_write_identity_protection_entities(self):
        """Test that WriteIdentityProtectionEntities.get_result() delegates to ReadIdentityProtectionEntities.get_result()"""
        # Create a mock ReadIdentityProtectionEntities instance
        mock_read_check = MagicMock()
        mock_read_check.get_result.return_value = IntegrationHealthCheckResult.PASSED

        # Create a mock integration with get_health_check method
        mock_integration = MagicMock()
        mock_integration.get_health_check.return_value = mock_read_check

        # Create the health check instance with the mock integration
        write_check = WriteIdentityProtectionEntities(integration=mock_integration)

        # Call the get_result method
        result = write_check.get_result()

        # Verify that get_health_check was called with ReadIdentityProtectionEntities
        mock_integration.get_health_check.assert_called_once_with(
            ReadIdentityProtectionEntities
        )

        # Verify that get_result was called on the returned health check
        mock_read_check.get_result.assert_called_once()

        # Verify the result
        self.assertEqual(result, IntegrationHealthCheckResult.PASSED)


class CrowdstrikeFalconIpV1HealthCheckComponentsTest(
    BaseTestCase, HealthCheckComponentTestMixin
):
    def setUp(self) -> None:
        super().setUp()
        self._patch_encryption()
        self.connector = ConnectorFactory(
            technology_id="crowdstrike_falcon_ip",
            version_id="v1",
            enabled_actions=[],
        )
        self.integration = self.connector.get_integration(decrypt_config=False)

    @patch.object(ConnectionHealthCheck, "get_result")
    @patch.object(ReadIdentityProtectionEntities, "get_result")
    @patch.object(WriteIdentityProtectionEntities, "get_result")
    def test_components(
        self,
        mock_write_entities_result,
        mock_read_entities_result,
        mock_connection_result,
    ):
        # Mock the health check methods to return PASSED
        mock_connection_result.return_value = IntegrationHealthCheckResult.PASSED
        mock_read_entities_result.return_value = IntegrationHealthCheckResult.PASSED
        mock_write_entities_result.return_value = IntegrationHealthCheckResult.PASSED

        # Get health check components
        components = HealthCheckComponent.get_components(connector=self.connector)

        # Define expected requirements for critical checks
        critical_checks_expected = [
            HealthCheckRequirement(
                name="Connection is valid",
                description="Can connect to the integration API",
                value=None,
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            ),
            HealthCheckRequirement(
                name="Read Identity Protection Entities",
                description="Read entities from Crowdstrike Identity Protection",
                value=None,
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            ),
            HealthCheckRequirement(
                name="Write Identity Protection Entities",
                description="Write to entities in Crowdstrike Identity Protection (watchlist operations)",
                value=None,
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            ),
        ]

        # Assert components match expected
        self.assert_components(components, [critical_checks_expected])

    @patch.object(ConnectionHealthCheck, "get_result")
    @patch.object(ReadIdentityProtectionEntities, "get_result")
    @patch.object(WriteIdentityProtectionEntities, "get_result")
    def test_components_connection_failed(
        self,
        mock_write_entities_result,
        mock_read_entities_result,
        mock_connection_result,
    ):
        # Mock the ConnectionHealthCheck.get_result method to return FAILED
        mock_connection_result.return_value = IntegrationHealthCheckResult.FAILED
        mock_read_entities_result.return_value = IntegrationHealthCheckResult.PASSED
        mock_write_entities_result.return_value = IntegrationHealthCheckResult.PASSED

        # Get health check components
        components = HealthCheckComponent.get_components(connector=self.connector)

        # Define expected requirements for critical checks
        critical_checks_expected = [
            HealthCheckRequirement(
                name="Connection is valid",
                description="Can connect to the integration API",
                value=None,
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.FAILED,
            ),
            HealthCheckRequirement(
                name="Read Identity Protection Entities",
                description="Read entities from Crowdstrike Identity Protection",
                value=None,
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            ),
            HealthCheckRequirement(
                name="Write Identity Protection Entities",
                description="Write to entities in Crowdstrike Identity Protection (watchlist operations)",
                value=None,
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            ),
        ]

        # Assert components match expected
        self.assert_components(components, [critical_checks_expected])


class CrowdstrikeFalconIpGraphQLTest(BaseIntegrationTest):
    technology_id = "crowdstrike_falcon_ip"
    version_id = "v1"

    @staticmethod
    def default_settings():
        return {}

    def setUp(self):
        super().setUp()
        self.integration = self.integration  # Already set up by BaseIntegrationTest
        self.user_id = "user-123456"
        self.host_id = "host-123456"

    @responses.activate
    def test_get_user_info(self):
        # Setup OAuth response
        setup_alerts_response(fetch_alerts=False)

        # Load the test data
        user_data = load_data("graphql/get_user_info_response.json")["data"]

        # Add the GraphQL response directly
        responses.add(
            responses.POST,
            "https://api.crowdstrike.com/identity-protection/api/graphql",
            json={"data": user_data},
            status=200,
        )

        # Create the args for the action
        args = UserIdentifierArgs(
            user_id=UserIdentifier(
                identifier_type="user_identifier", value_type=None, value=self.user_id
            )
        )

        # Execute the action
        self.integration.invoke_action(
            IntegrationActionType.GET_USER_INFO,
            action_args=args,
        )

    @responses.activate
    def test_get_user_info_not_found(self):
        """Test that get_user_info returns an error when the user is not found."""
        # Setup OAuth response
        setup_alerts_response(fetch_alerts=False)

        # Mock the GraphQL API to return empty nodes
        responses.add(
            responses.POST,
            "https://api.crowdstrike.com/identity-protection/api/graphql",
            json={"data": {"entities": {"nodes": []}}},
            status=200,
        )

        # Create the args for the action
        args = UserIdentifierArgs(
            user_id=UserIdentifier(
                identifier_type="user_identifier",
                value_type=None,
                value="nonexistent-user",
            )
        )

        # Execute the action
        result = self.integration.invoke_action(
            IntegrationActionType.GET_USER_INFO,
            action_args=args,
        )

        # Verify that an error is returned
        self.assertIsNone(result.result)
        self.assertIsNotNone(result.error)
        self.assertEqual(result.error.message, "User nonexistent-user not found")

    @responses.activate
    def test_add_user_to_watchlist(self):
        # Setup OAuth response
        setup_alerts_response(fetch_alerts=False)

        # Load the test data
        watchlist_data = load_data("graphql/add_user_to_watchlist_response.json")[
            "data"
        ]

        # Add the GraphQL response directly
        responses.add(
            responses.POST,
            "https://api.crowdstrike.com/identity-protection/api/graphql",
            json={"data": watchlist_data},
            status=200,
        )

        # Create the args for the action
        args = UserIdentifierArgs(
            user_id=UserIdentifier(
                identifier_type="user_identifier", value_type=None, value=self.user_id
            )
        )

        # Execute the action
        result = self.integration.invoke_action(
            IntegrationActionType.ADD_USER_TO_WATCHLIST,
            action_args=args,
        )

        # Verify the result
        # TAPResult doesn't have a success attribute, but if no exception was raised, the action was successful
        self.assertIsNone(result.error)
        # TAPResult[Message] should have a result attribute with a message
        # But the current implementation doesn't set it, so we can't assert on it
        # self.assertEqual(result.result.message, "User added to watchlist successfully")

    @responses.activate
    def test_remove_user_from_watchlist(self):
        # Setup OAuth response
        setup_alerts_response(fetch_alerts=False)

        # Load the test data
        watchlist_data = load_data("graphql/remove_user_from_watchlist_response.json")[
            "data"
        ]

        # Add the GraphQL response directly
        responses.add(
            responses.POST,
            "https://api.crowdstrike.com/identity-protection/api/graphql",
            json={"data": watchlist_data},
            status=200,
        )

        # Create the args for the action
        args = UserIdentifierArgs(
            user_id=UserIdentifier(
                identifier_type="user_identifier", value_type=None, value=self.user_id
            )
        )

        # Execute the action
        result = self.integration.invoke_action(
            IntegrationActionType.REMOVE_USER_FROM_WATCHLIST,
            action_args=args,
        )

        # Verify the result
        self.assertIsNone(result.error)
        self.assertEqual(
            result.result.message, f"User {self.user_id} removed from watchlist."
        )

    @responses.activate
    def test_get_host_info(self):
        # Setup OAuth response
        setup_alerts_response(fetch_alerts=False)

        # Load the test data
        host_data = load_data("graphql/get_host_info_response.json")["data"]

        # Add the GraphQL response directly
        responses.add(
            responses.POST,
            "https://api.crowdstrike.com/identity-protection/api/graphql",
            json={"data": host_data},
            status=200,
        )

        # Create the args for the action
        args = HostIdentifierArgs(
            host=HostIdentifier(
                identifier_type="host_identifier", value_type=None, value=self.host_id
            )
        )

        # Execute the action
        self.integration.invoke_action(
            IntegrationActionType.GET_HOST_INFO,
            action_args=args,
        )

    @responses.activate
    def test_get_host_info_not_found(self):
        """Test that get_host_info returns an error when the host is not found."""
        # Setup OAuth response
        setup_alerts_response(fetch_alerts=False)

        # Mock the GraphQL API to return empty nodes
        responses.add(
            responses.POST,
            "https://api.crowdstrike.com/identity-protection/api/graphql",
            json={"data": {"entities": {"nodes": []}}},
            status=200,
        )

        # Create the args for the action
        args = HostIdentifierArgs(
            host=HostIdentifier(
                identifier_type="host_identifier",
                value_type=None,
                value="nonexistent-host",
            )
        )

        # Execute the action
        result = self.integration.invoke_action(
            IntegrationActionType.GET_HOST_INFO,
            action_args=args,
        )

        # Verify that an error is returned
        self.assertIsNone(result.result)
        self.assertIsNotNone(result.error)
        self.assertEqual(result.error.message, "Host nonexistent-host not found")

    @responses.activate
    def test_add_host_to_watchlist(self):
        # Setup OAuth response
        setup_alerts_response(fetch_alerts=False)

        # Load the test data
        watchlist_data = load_data("graphql/add_host_to_watchlist_response.json")[
            "data"
        ]

        # Add the GraphQL response directly
        responses.add(
            responses.POST,
            "https://api.crowdstrike.com/identity-protection/api/graphql",
            json={"data": watchlist_data},
            status=200,
        )

        # Create the args for the action
        args = HostIdentifierArgs(
            host=HostIdentifier(
                identifier_type="host_identifier", value_type=None, value=self.host_id
            )
        )

        # Execute the action
        result = self.integration.invoke_action(
            IntegrationActionType.ADD_HOST_TO_WATCHLIST,
            action_args=args,
        )

        # Verify the result
        self.assertIsNone(result.error)
        self.assertEqual(
            result.result.message, f"Host {self.host_id} added to watchlist."
        )

    @responses.activate
    def test_remove_host_from_watchlist(self):
        # Setup OAuth response
        setup_alerts_response(fetch_alerts=False)

        # Load the test data
        watchlist_data = load_data("graphql/remove_host_from_watchlist_response.json")[
            "data"
        ]

        # Add the GraphQL response directly
        responses.add(
            responses.POST,
            "https://api.crowdstrike.com/identity-protection/api/graphql",
            json={"data": watchlist_data},
            status=200,
        )

        # Create the args for the action
        args = HostIdentifierArgs(
            host=HostIdentifier(
                identifier_type="host_identifier", value_type=None, value=self.host_id
            )
        )

        # Execute the action
        result = self.integration.invoke_action(
            IntegrationActionType.REMOVE_HOST_FROM_WATCHLIST,
            action_args=args,
        )

        # Verify the result
        self.assertIsNone(result.error)
        self.assertEqual(
            result.result.message, f"Host {self.host_id} removed from watchlist."
        )

    def test_map_risk_level(self):
        # Test all possible mappings for risk level
        self.assertEqual(
            map_risk_level("NORMAL"),
            RiskLevel.LOW,
        )

        self.assertEqual(
            map_risk_level("MEDIUM"),
            RiskLevel.MEDIUM,
        )

        self.assertEqual(
            map_risk_level("HIGH"),
            RiskLevel.HIGH,
        )

        # Test default case
        self.assertEqual(
            map_risk_level("UNKNOWN"),
            RiskLevel.OTHER,
        )

    def test_map_operating_system(self):
        # Test all possible mappings for OS
        self.assertEqual(
            map_operating_system("WINDOWS"),
            OSType.WINDOWS,
        )

        self.assertEqual(
            map_operating_system("OSX"),
            OSType.MACOS,
        )

        self.assertEqual(
            map_operating_system("UNIX"),
            OSType.LINUX,
        )

        self.assertEqual(
            map_operating_system("LINUX"),
            OSType.LINUX,
        )

        self.assertEqual(
            map_operating_system("IOS"),
            OSType.IOS,
        )

        self.assertEqual(
            map_operating_system("ANDROID"),
            OSType.ANDROID,
        )

        self.assertEqual(
            map_operating_system("OTHER"),
            OSType.OTHER,
        )

    def test_map_target_type(self):
        # Test all possible mappings for target type
        self.assertEqual(
            map_target_type("WORKSTATION"),
            EndpointType.LAPTOP,
        )

        self.assertEqual(
            map_target_type("SERVER"),
            EndpointType.SERVER,
        )

        self.assertEqual(
            map_target_type("INTEGRATED_SOLUTION_APPLIANCE"),
            "INTEGRATED_SOLUTION_APPLIANCE",
        )

        self.assertEqual(
            map_target_type("MOBILE"),
            EndpointType.MOBILE,
        )

        self.assertEqual(
            map_target_type("TABLET"),
            EndpointType.TABLET,
        )

        self.assertEqual(
            map_target_type("GAME_CONSOLE"),
            "GAME_CONSOLE",
        )

        self.assertEqual(
            map_target_type("WEARABLE"),
            "WEARABLE",
        )

        self.assertEqual(
            map_target_type("SMART_TV"),
            "SMART_TV",
        )

        self.assertEqual(
            map_target_type("PDA"),
            "PDA",
        )

        self.assertEqual(
            map_target_type("UNDETERMINED"),
            EndpointType.UNKNOWN,
        )

        # Test default case
        self.assertEqual(
            map_target_type("UNKNOWN_TYPE"),
            "UNKNOWN_TYPE",
        )


class CrowdstrikeFalconIpEventSyncTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.integration = ConnectorFactory.get_integration(
            technology_id="crowdstrike_falcon_ip"
        )
        self.timestamp = "2025-01-24T16:17:21.531611083Z"

    def test_bookmarks(self):
        bookmark = CrowdstrikeFalconIpV1EventSyncBookmark(
            last_event_ingested="2025-01-24T16:17:21.531611083Z"
        )
        self.assertIsNotNone(bookmark)

        schema = CrowdstrikeFalconIpV1EventSyncBookmark.model_json_schema()
        self.assertIn("last_event_ingested", schema["properties"])

    def expected_alert_response(
        self,
        created_timestamp=None,
    ):
        """Helper function to create expected event response"""

        if created_timestamp is None:
            created_timestamp = self.timestamp

        # Create the raw event
        raw_event = {
            "activity_id": "E7EDxxxxxxxxxxxC8F9",
            "agent_id": "0edfxxxxxxxxxxx0dae",
            "aggregate_id": "aggind:0877xxxxxxxxxxx0121:1CAFxxxxxxxxxxx81DE",
            "alert_attributes": "0",
            "cid": "0877xxxxxxxxxxx0121",
            "composite_id": "087xxxxxxxxxxx0121:ind:0877dxxxxxxxxxxx0121:1CAFxxxxxxxxxxx1DE",
            "confidence": 20,
            "context_timestamp": "2025-04-22T11:41:26.000Z",
            "crawled_timestamp": "2025-04-22T12:41:27.968526106Z",
            "created_timestamp": created_timestamp,
            "data_domains": ["Identity"],
            "description": "A user logged in to a machine for the first time",
            "display_name": "Unusual login to an endpoint",
            "end_time": "2025-04-22T11:39:10.000Z",
            "falcon_host_link": "https://falcon.us-2.crowdstrike.com/identity-protection/detections/087xxxxxxxxxxx0121:ind:0877dxxxxxxxxxxx0121:1CAFxxxxxxxxxxx1DE?_cid=g04000f3zvlk6plfazralhaf2qqfpyuy",
            "id": "ind:087xxxxxxxxxxx0121:ind:0877dxxxxxxxxxxx0121:1CAFxxxxxxxxxxx1DE",
            "idp_policy_rule_id": "test_idp_policy_rule_id",
            "idp_policy_rule_name": "test_idp_policy_rule_name",
            "name": "AnomalousNewEndpointUsage",
            "objective": "Gain Access",
            "pattern_id": 51135,
            "poly_id": "AAAI087xxxxxxxxxxx0121:ind:0877dxxxxxxxxxxx0121:1CAFxxxxxxxxxxx1DE3g==",
            "product": "idp",
            "scenario": "machine_learning",
            "seconds_to_resolved": 0,
            "seconds_to_triaged": 0,
            "azure_application_id": "123456",
            "severity": 10,
            "severity_name": "Informational",
            "show_in_ui": True,
            "source_account_domain": "TEST.COM",
            "source_account_name": "test",
            "source_account_object_guid": "BA5xxxxxxxxxxxE1",
            "source_account_object_sid": "S-1-5xxxxxxxxxxx1-19963",
            "source_account_upn": "<EMAIL>",
            "source_endpoint_account_object_guid": "5D7xxxxxxxxxxxB2E",
            "source_endpoint_account_object_sid": "S-1-xxxxxxxxxxx7912",
            "source_endpoint_address_ip4": "************",
            "source_endpoint_host_name": "pc-xxxxxxxxxxxcom",
            "source_endpoint_ip_address": "************",
            "source_endpoint_sensor_id": "0edfdxxxxxxxxxxxbf0dae",
            "source_products": ["Falcon Identity Protection"],
            "source_vendors": ["CrowdStrike"],
            "start_time": "2025-04-22T11:39:10.000Z",
            "status": "new",
            "tactic": "Initial Access",
            "tactic_id": "TA0001",
            "target_account_object_sid": "S-1-5-2xxxxxxxxxxx1-502",
            "target_service_access_identifier": "krbtgt/TEST.COM",
            "technique": "Valid Accounts",
            "technique_id": "T1078",
            "timestamp": "2025-04-22T11:41:26.256Z",
            "type": "idp-session-source-user-endpoint-target-info",
            "updated_timestamp": "2025-04-22T12:41:27.968510379Z",
            "user_name": "test",
        }

        # Parse the timestamp to get the expected format
        from dateutil import parser

        formatted_created_timestamp = parser.isoparse(created_timestamp)
        formatted_created_timestamp = formatted_created_timestamp.strftime(
            "%Y-%m-%dT%H:%M:%S.%fZ"
        ).replace(".000000Z", ".0Z")

        # Create the expected OCSF response with all required fields
        ocsf_response = {
            "activity_id": 1,
            "activity_name": "Create",
            "category_name": "Findings",
            "category_uid": 2,
            "class_name": "Detection Finding",
            "class_uid": 2004,
            "end_time_dt": "2025-04-22T11:39:10Z",
            "message": "A user logged in to a machine for the first time",
            "metadata": {
                "correlation_uid": "087xxxxxxxxxxx0121:ind:0877dxxxxxxxxxxx0121:1CAFxxxxxxxxxxx1DE",
                "event_code": "idp-session-source-user-endpoint-target-info",
                "product": {
                    "feature": {"name": "idp"},
                    "name": "CrowdStrike Falcon Identity Protection",
                    "vendor_name": "CrowdStrike",
                },
                "profiles": ["cloud", "datetime", "incident"],
                "tenant_uid": "0877xxxxxxxxxxx0121",
                "version": "1.5.0-dev",
            },
            "severity": "Informational",
            "severity_id": 1,
            "start_time_dt": "2025-04-22T11:39:10Z",
            "status": "New",
            "status_id": 1,
            "time_dt": "2025-04-22T11:41:26.256000Z",
            "type_name": "Detection Finding: Create",
            "type_uid": 200401,
            "verdict": "Unknown",
            "verdict_id": 0,
            "confidence_score": 20,
            "cloud": {
                "account": {"type": "Azure AD Account", "type_id": 6, "uid": "123456"},
                "provider": "Azure",
            },
            "evidences": [
                {
                    "actor": {
                        "user": {
                            "account": None,
                            "domain": "TEST.COM",
                            "name": "test",
                            "uid": "BA5xxxxxxxxxxxE1",
                            "uid_alt": "<EMAIL>",
                        }
                    },
                    "device": None,
                    "dst_endpoint": {
                        "ip": None,
                        "uid": "",
                        "owner": {
                            "uid": "",
                            "uid_alt": None,
                        },
                        "hostname": None,
                    },
                    "src_endpoint": {
                        "ip": "************",
                        "uid": "0edfdxxxxxxxxxxxbf0dae",
                        "owner": {
                            "uid": "5D7xxxxxxxxxxxB2E",
                            "uid_alt": "S-1-xxxxxxxxxxx7912",
                        },
                        "hostname": "pc-xxxxxxxxxxxcom",
                    },
                    "user": {
                        "domain": None,
                        "name": "",
                        "uid": "S-1-5-2xxxxxxxxxxx1-502",
                        "uid_alt": None,
                    },
                }
            ],
            "finding_info": {
                "analytic": {"uid": "51135"},
                "attacks": [
                    {
                        "tactic": {"name": "Initial Access", "uid": "TA0001"},
                        "technique": {"name": "Valid Accounts", "uid": "T1078"},
                    }
                ],
                "created_time_dt": formatted_created_timestamp,
                "desc": "A user logged in to a machine for the first time",
                "modified_time_dt": "2025-04-22T12:41:27.968510Z",
                "product": {
                    "feature": {"name": "idp"},
                    "name": "Falcon Identity Protection",
                    "vendor_name": "CrowdStrike",
                },
                "src_url": "https://falcon.us-2.crowdstrike.com/identity-protection/detections/087xxxxxxxxxxx0121:ind:0877dxxxxxxxxxxx0121:1CAFxxxxxxxxxxx1DE?_cid=g04000f3zvlk6plfazralhaf2qqfpyuy",
                "title": "Unusual login to an endpoint",
                "uid": "087xxxxxxxxxxx0121:ind:0877dxxxxxxxxxxx0121:1CAFxxxxxxxxxxx1DE",
            },
            "policy": {
                "uid": "test_idp_policy_rule_id",
                "name": "test_idp_policy_rule_name",
            },
            "time": 1745322086256,
        }

        return {
            "raw_event": raw_event,
            "event_timestamp": "2025-04-22T11:41:26.256000Z",
            "ioc": {
                "external_id": "51135",
                "external_name": "Unusual login to an endpoint",
                "has_ioc_definition": False,
                "mitre_techniques": ["T1078"],
            },
            "vendor_item_ref": {
                "id": "087xxxxxxxxxxx0121:ind:0877dxxxxxxxxxxx0121:1CAFxxxxxxxxxxx1DE",
                "title": "Unusual login to an endpoint",
                "url": "https://falcon.us-2.crowdstrike.com/identity-protection/detections/087xxxxxxxxxxx0121:ind:0877dxxxxxxxxxxx0121:1CAFxxxxxxxxxxx1DE?_cid=g04000f3zvlk6plfazralhaf2qqfpyuy",
                "created": formatted_created_timestamp,
            },
            "vendor_group_ref": None,
            "ocsf": ocsf_response,
        }

    @responses.activate
    def test_event_sync(self):
        # Clear any existing responses
        responses.reset()

        bookmark = self.integration.get_bookmark(IntegrationActionType.EVENT_SYNC)
        self.assertIsNotNone(bookmark)
        bookmark.last_event_ingested = datetime.fromisoformat(
            self.timestamp
        ) - timedelta(hours=1)

        setup_alerts_response(created_timestamp=self.timestamp)

        # Execute the event sync action
        response = self.integration.invoke_action(
            IntegrationActionType.EVENT_SYNC,
            action_args=EventSyncArgs(),
            bookmark=bookmark,
        )

        from apps.connectors.utils import serialize

        result = serialize(list(response))
        expected_result = self.expected_alert_response(created_timestamp=self.timestamp)

        # Compare the entire result with expected responses
        self.assertEqual(
            result,
            [expected_result],
        )

        # Verify bookmark was updated with the latest timestamp
        self.assertEqual(
            bookmark.last_event_ingested,
            datetime.fromisoformat(self.timestamp),
        )

    @responses.activate
    def test_event_sync_with_newer_alert_time(self):
        responses.reset()

        newer_timestamp = "2025-01-25T10:00:00.000000000Z"
        older_timestamp = "2025-01-24T16:17:21.531611083Z"
        bookmark = CrowdstrikeFalconIpV1EventSyncBookmark(
            last_event_ingested=datetime.fromisoformat(older_timestamp)
            - timedelta(hours=1)
        )

        setup_alerts_response(created_timestamp=newer_timestamp)

        event_sync_action = self.integration.get_action(
            IntegrationActionType.EVENT_SYNC
        )

        for _ in event_sync_action.execute(EventSyncArgs(), bookmark=bookmark):
            pass

        self.assertEqual(
            bookmark.last_event_ingested, datetime.fromisoformat(newer_timestamp)
        )

    @responses.activate
    def test_map_alert_severity(self):
        from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.actions.event_sync import (
            map_ocsf_severity,
        )

        severity_map = {
            11: ocsf.Severity.INFORMATIONAL,
            31: ocsf.Severity.LOW,
            51: ocsf.Severity.MEDIUM,
            71: ocsf.Severity.HIGH,
            91: ocsf.Severity.CRITICAL,
            None: ocsf.Severity.UNKNOWN,
        }

        for severity, expected_severity in severity_map.items():
            self.assertEqual(
                map_ocsf_severity(severity),
                expected_severity,
            )

    @responses.activate
    def test_map_alert_status(self):
        from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.actions.event_sync import (
            map_ocsf_status,
        )

        status_map = {
            "new": ocsf.DetectionStatus.NEW,
            "in_progress": ocsf.DetectionStatus.IN_PROGRESS,
            "closed": ocsf.DetectionStatus.RESOLVED,
            "reopened": ocsf.DetectionStatus.NEW,
            None: ocsf.DetectionStatus.UNKNOWN,
            "random": ocsf.DetectionStatus.UNKNOWN,
        }

        for status, expected_status in status_map.items():
            self.assertEqual(
                map_ocsf_status(status),
                expected_status,
            )

    def test_map_source_account_to_evidence(self):
        from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.actions.event_sync import (
            map_ocsf_evidences,
        )

        event = {
            "source_account_object_guid": "BA5xxxxxxxxxxxE1",
            "source_account_object_sid": "S-1-5xxxxxxxxxxx1-19963",
            "source_account_domain": "TEST.COM",
            "source_account_name": "test",
            "source_account_azure_id": "123456",
        }

        result = map_ocsf_evidences(event)
        self.assertTrue(len(result) == 1)
        evidence = result[0]
        self.assertEqual(
            evidence.actor.user.account.type,
            "Azure AD Account",
        )
        self.assertEqual(evidence.actor.user.name, "test")
        self.assertEqual(evidence.actor.user.domain, "TEST.COM")
        self.assertEqual(
            evidence.actor.user.uid,
            "BA5xxxxxxxxxxxE1",
        )
        self.assertEqual(
            evidence.actor.user.uid_alt,
            "123456",
        )

        # Test with Okta id
        event = {
            "source_account_object_guid": "BA5xxxxxxxxxxxE1",
            "source_account_object_sid": "S-1-5xxxxxxxxxxx1-19963",
            "source_account_domain": "TEST.COM",
            "source_account_name": "test",
            "source_account_okta_id": "123456",
        }
        result = map_ocsf_evidences(event)
        self.assertTrue(len(result) == 1)
        evidence = result[0]
        self.assertEqual(
            evidence.actor.user.account.type,
            "Okta",
        )
        self.assertEqual(
            evidence.actor.user.uid_alt,
            "123456",
        )

    def test_map_target_account_to_evidence(self):
        from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.actions.event_sync import (
            map_ocsf_evidences,
        )

        event = {
            "target_account_object_sid": "BA5xxxxxxxxxxxE1",
            "target_account_upn": "<EMAIL>",
            "target_account_domain": "TEST.COM",
            "target_account_name": "test",
        }

        result = map_ocsf_evidences(event)
        self.assertTrue(len(result) == 1)
        evidence = result[0]
        self.assertEqual(evidence.user.name, "test")
        self.assertEqual(evidence.user.domain, "TEST.COM")
        self.assertEqual(
            evidence.user.uid,
            "BA5xxxxxxxxxxxE1",
        )
        self.assertEqual(
            evidence.user.uid_alt,
            "<EMAIL>",
        )

    def test_map_location_to_evidence(self):
        from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.actions.event_sync import (
            map_ocsf_evidences,
        )

        event = {
            "location_latitude": 39.2,
            "location_longitude": 76.6,
            "location_country_code": "US",
        }

        result = map_ocsf_evidences(event)
        self.assertTrue(len(result) == 1)
        evidence = result[0]
        self.assertEqual(evidence.device.location.lat, 39.2)
        self.assertEqual(evidence.device.location.long, 76.6)
        self.assertEqual(evidence.device.location.country, "US")

    def test_source_endpoint_to_evidence(self):
        from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.actions.event_sync import (
            map_ocsf_evidences,
        )

        event = {
            "source_endpoint_account_object_guid": "5D7xxxxxxxxxxxB2E",
            "source_endpoint_account_object_sid": "S-1-xxxxxxxxxxx7912",
            "source_endpoint_ip_address": "***********",
            "source_endpoint_host_name": "pc-xxxxxxxxxxxcom",
            "source_endpoint_sensor_id": "0edfdxxxxxxxxxxxbf0dae",
        }

        result = map_ocsf_evidences(event)
        self.assertTrue(len(result) == 1)
        evidence = result[0]
        self.assertEqual(evidence.src_endpoint.ip, "***********")
        self.assertEqual(
            evidence.src_endpoint.owner.uid,
            "5D7xxxxxxxxxxxB2E",
        )
        self.assertEqual(
            evidence.src_endpoint.owner.uid_alt,
            "S-1-xxxxxxxxxxx7912",
        )
        self.assertEqual(evidence.src_endpoint.hostname, "pc-xxxxxxxxxxxcom")
        self.assertEqual(
            evidence.src_endpoint.uid,
            "0edfdxxxxxxxxxxxbf0dae",
        )

    def test_target_endpoint_to_evidence(self):
        from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.actions.event_sync import (
            map_ocsf_evidences,
        )

        event = {
            "target_endpoint_account_object_guid": "5D7xxxxxxxxxxxB2E",
            "target_endpoint_account_object_sid": "S-1-xxxxxxxxxxx7912",
            "target_endpoint_ip_address": "***********",
            "target_endpoint_host_name": "pc-xxxxxxxxxxxcom",
            "target_endpoint_sensor_id": "0edfdxxxxxxxxxxxbf0dae",
        }

        result = map_ocsf_evidences(event)
        self.assertTrue(len(result) == 1)
        evidence = result[0]
        self.assertEqual(evidence.dst_endpoint.ip, "***********")
        self.assertEqual(
            evidence.dst_endpoint.owner.uid,
            "5D7xxxxxxxxxxxB2E",
        )
        self.assertEqual(
            evidence.dst_endpoint.owner.uid_alt,
            "S-1-xxxxxxxxxxx7912",
        )
        self.assertEqual(evidence.dst_endpoint.hostname, "pc-xxxxxxxxxxxcom")
        self.assertEqual(
            evidence.dst_endpoint.uid,
            "0edfdxxxxxxxxxxxbf0dae",
        )

    def test_okta_application_id_to_cloud(self):
        from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.actions.event_sync import (
            convert_to_ocsf,
        )

        event = {
            "okta_application_id": "123456",
            "source_products": ["Falcon Identity Protection"],
            "product": "idp",
            "display_name": "Unusual login to an endpoint",
            "description": "A user logged in to a machine for the first time",
            "created_timestamp": "2025-04-22T11:41:26.256Z",
            "updated_timestamp": "2025-04-22T12:41:27.968510Z",
            "timestamp": "2025-04-22T11:41:26.256Z",
            "composite_id": "087xxxxxxxxxxx0121:ind:0877dxxxxxxxxxxx0121:1CAFxxxxxxxxxxx1DE",
        }

        finding = convert_to_ocsf(event)
        self.assertTrue(finding.cloud.account.type == "Okta")
        self.assertTrue(finding.cloud.account.uid == "123456")
