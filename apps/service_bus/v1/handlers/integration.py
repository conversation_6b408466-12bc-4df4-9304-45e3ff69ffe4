import logging

from criticalstart.service_bus import Message, notifications

from apps.connectors.integrations.vendors.microsoft.ms_xdr.template import MsXdrTemplate
from apps.connectors.models.connector import Connector
from apps.connectors.tasks.task_sync_events import ms_xdr_event_sync_completed
from apps.service_bus.tasks import compile_results
from apps.service_bus.v1.schemas.integration import IntegrationActionType

logger = logging.getLogger(__name__)


def integration_invoke_command_handler(message: Message) -> None:
    compile_results.delay(
        message.correlation_id,
        message.sender_id,
        message.body.command,
        message.body.model_dump_json(),
    )

    message.acknowledge()


def integration_action_complete_handler(
    notification: notifications.IntegrationActionCompleted,
):
    if notification.action != IntegrationActionType.EVENT_SYNC:
        return

    integration_id = notification.object.id
    connector = Connector.objects.get(id=integration_id)

    if connector.technology_id == MsXdrTemplate.id:
        ms_xdr_event_sync_completed.delay(
            integration_id=integration_id,
            organization_id=connector.organization_id,
            result_ids=notification.result_ids,
        )
