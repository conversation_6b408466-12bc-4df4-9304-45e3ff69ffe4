import uuid
from unittest.mock import MagicMock, call, patch

import responses
from django.test import TransactionTestCase, override_settings
from django.utils import timezone

from apps.connectors.integrations.actions.action import (
    IntegrationActionType as IntegrationActionTypeModel,
)
from apps.connectors.integrations.template import Template
from apps.connectors.integrations.vendors.vendor import Vendors
from apps.connectors.services import connector_service
from apps.connectors.services.artifact_service import artifact_service
from apps.service_bus.v1.handlers.integration import (
    integration_action_complete_handler,
    integration_invoke_command_handler,
)
from apps.service_bus.v1.schemas.integration import (
    IntegrationActionType,
    IntegrationInvokeActionCommand,
    IntegrationInvokeCommandResponse,
)
from apps.tests.base import BaseCaseMixin
from factories import ConnectorFactory, OrganizationFactory
from factories.aad_app import AadAppFactory


@override_settings(CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=False)
class IntegrationInvokeActionCommandTests(TransactionTestCase, BaseCaseMixin):
    def setUp(self) -> None:
        super().setUp()

        self._patch_encryption()

        self.organization = OrganizationFactory()

        self.connector = ConnectorFactory(
            organization=self.organization,
            technology_id="sentinel_one",
            version_id="v2_1",
            last_activity_at=timezone.now(),
        )

    @staticmethod
    def _mock_api():
        responses.get(
            "https://test_url.com/web/api/v2.1/agents",
            json={
                "data": [{"device_id": "test_device_id"}],
                "pagination": {"nextCursor": None},
            },
        )

    @responses.activate
    @patch.object(connector_service, "is_healthy", return_value=True)
    @patch("core.service_bus.service_bus")
    @patch.object(artifact_service, "convert_key_to_id", return_value="test_aid")
    @patch("criticalstart.service_bus.Message")
    def test_invoke_integration_action(
        self,
        message,
        convert_key_to_id,
        service_bus,
        is_healthy,
    ):
        self._mock_api()

        last_activity_at = self.connector.last_activity_at

        service_bus.send_command_response = MagicMock()

        command = IntegrationInvokeActionCommand(
            integration_id=self.connector.id,
            action=IntegrationActionType.HOST_SYNC,
            action_args={},
        )
        message.correlation_id = uuid.uuid4()
        message.sender_id = "test_sender_id"
        message.body = command

        integration_invoke_command_handler(message)

        # Dynamically generate the expected artifact_keys value
        expected_artifact_keys = [
            f"organization_id={self.connector.organization.id}/technology_id={self.connector.technology_id}/v2_1/connector_id={self.connector.id}/year={timezone.now().year}/month={timezone.now().strftime('%m')}/day={timezone.now().strftime('%d')}/{message.correlation_id}.jsonl"
        ]

        response_body = IntegrationInvokeCommandResponse(
            status="SUCCESS",
            artifact_ids=[convert_key_to_id.return_value],
            artifact_keys=expected_artifact_keys,
            artifact_filesystem_url=artifact_service.get_file_system_url(),
            return_type="Host",
        )

        service_bus.send_command_response.assert_called_once_with(
            command=IntegrationInvokeActionCommand.command,
            command_response_body=response_body,
            correlation_id=message.correlation_id,
            sender_id=message.sender_id,
        )

        message.acknowledge.assert_called_once()

        self.connector.refresh_from_db()
        self.assertGreater(self.connector.last_activity_at, last_activity_at)
        self.assert_activity_logs(
            self.connector,
            [
                r"Fetch hosts started\. Correlation ID: [a-z0-9]+;",
                r"Validation Item 1: 'id' not found",
                r"Fetch hosts .*\. Elapsed time: \d+\.\d+ seconds; Anomalies: 1; Correlation ID: [a-z0-9]+;",
            ],
        )

    @responses.activate
    @patch("core.service_bus.service_bus")
    @patch("criticalstart.service_bus.Message")
    def test_invoke_integration_action_non_existent_integration(
        self, message, service_bus
    ):
        self._mock_api()

        last_activity_at = self.connector.last_activity_at

        service_bus.send_command_response = MagicMock()

        service_bus.send_command_response = MagicMock()

        command = IntegrationInvokeActionCommand(
            integration_id=uuid.uuid4(),  # won't find this one
            action=IntegrationActionType.HOST_SYNC,
            action_args={},
        )
        message.correlation_id = uuid.uuid4()
        message.sender_id = "test_sender_id"
        message.body = command

        integration_invoke_command_handler(message)

        response_body = IntegrationInvokeCommandResponse(
            status="FAILURE", artifact_ids=None
        )

        service_bus.send_command_response.assert_called_once_with(
            command=IntegrationInvokeActionCommand.command,
            command_response_body=response_body,
            correlation_id=message.correlation_id,
            sender_id=message.sender_id,
        )

        message.acknowledge.assert_called_once()

        self.connector.refresh_from_db()
        self.assertEqual(self.connector.last_activity_at, last_activity_at)

        self.assert_activity_logs(self.connector, ["Integration is not healthy."])

    @responses.activate
    @patch("core.service_bus.service_bus")
    @patch.object(artifact_service, "convert_key_to_id", side_effect=Exception)
    @patch("criticalstart.service_bus.Message")
    def test_invoke_integration_action_failure(
        self, message, convert_key_to_id, service_bus
    ):
        self._mock_api()

        last_activity_at = self.connector.last_activity_at

        service_bus.send_command_response = MagicMock()

        command = IntegrationInvokeActionCommand(
            integration_id=self.connector.id,
            action=IntegrationActionType.HOST_SYNC,
            action_args={},
        )
        message.correlation_id = uuid.uuid4()
        message.sender_id = "test_sender_id"
        message.body = command

        integration_invoke_command_handler(message)

        response_body = IntegrationInvokeCommandResponse(
            status="UNHEALTHY", artifact_ids=None
        )

        service_bus.send_command_response.assert_called_once_with(
            command=IntegrationInvokeActionCommand.command,
            command_response_body=response_body,
            correlation_id=message.correlation_id,
            sender_id=message.sender_id,
        )

        message.acknowledge.assert_called_once()

        self.connector.refresh_from_db()
        self.assertEqual(self.connector.last_activity_at, last_activity_at)

        self.assert_activity_logs(
            self.connector, ["Error while running health check 'Connection is valid'"]
        )

    def assert_activity_logs(self, connector, expected: list):
        messages = [item.message for item in connector.activity_logs.all()]
        for message, expected in zip(messages, expected):
            self.assertRegex(message, expected)


@override_settings(CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=False)
class MsXdrEventSyncActionCompletedTests(TransactionTestCase, BaseCaseMixin):
    def setUp(self) -> None:
        super().setUp()

        self._patch_encryption()

        self.organization = OrganizationFactory()
        aad_app = AadAppFactory()
        self.xdr_connector = ConnectorFactory(
            technology_id="ms_xdr",
            config__client_id=aad_app.client_id,
            organization=self.organization,
            enabled_actions=[IntegrationActionTypeModel.EVENT_SYNC_FROM_ARTIFACT],
        )
        self.abnormal_security_connector = ConnectorFactory(
            technology_id="abnormal_security",
            organization=self.organization,
        )

        self.ms_related_integrations = [
            self.xdr_connector,
        ]
        for template in Template.get_all_templates():
            if template.id == "ms_xdr":
                continue

            if template.vendor_id == Vendors.MICROSOFT.id:
                for version in template.versions.values():
                    for action in version.integration.actions:
                        if (
                            action.action_type
                            == IntegrationActionTypeModel.EVENT_SYNC_FROM_ARTIFACT
                        ):
                            connector = ConnectorFactory(
                                technology_id=template.id,
                                organization=self.organization,
                                config__client_id=aad_app.client_id,
                                enabled=True,
                                enabled_actions=[
                                    IntegrationActionTypeModel.EVENT_SYNC_FROM_ARTIFACT
                                ],
                            )
                            self.ms_related_integrations.append(connector)

    @patch("apps.service_bus.v1.handlers.integration.ms_xdr_event_sync_completed.delay")
    def test_non_event_sync_complete(self, ms_xdr_event_sync_completed):
        notification = MagicMock()
        notification.action = IntegrationActionType.HOST_SYNC
        notification.object.id = self.xdr_connector.id
        notification.result_ids = ["test_result_id"]

        integration_action_complete_handler(notification)

        ms_xdr_event_sync_completed.assert_not_called()

    @patch("apps.service_bus.v1.handlers.integration.ms_xdr_event_sync_completed.delay")
    def test_event_sync_complete_non_ms_xdr(self, ms_xdr_event_sync_completed):
        notification = MagicMock()
        notification.action = IntegrationActionType.EVENT_SYNC
        notification.object.id = self.abnormal_security_connector.id
        notification.result_ids = ["test_result_id"]

        integration_action_complete_handler(notification)

        ms_xdr_event_sync_completed.assert_not_called()

    @patch("apps.connectors.tasks.task_sync_events.invoke_integration_action.apply")
    def test_event_sync_complete_ms_xdr(self, invoke_integration_action):
        notification = MagicMock()
        notification.action = IntegrationActionType.EVENT_SYNC
        notification.object.id = self.xdr_connector.id
        notification.result_ids = ["test_result_id"]

        integration_action_complete_handler(notification)

        expected_calls = [
            call(
                args=(
                    integration.id,
                    IntegrationActionTypeModel.EVENT_SYNC_FROM_ARTIFACT,
                    {"artifact_id": "test_result_id"},
                )
            )
            for integration in self.ms_related_integrations
        ]
        invoke_integration_action.assert_has_calls(expected_calls, any_order=True)
