#!/usr/bin/env python3

# CLI to create a new integration from a template
# parameters are id, name, category, version_id, vendor_id, and supported_actions
# The result should be a new module in apps\connectors\integrations\vendors
# The new module should have a template.py file
# The new module should have a folder based on version_id
# The new module should have an integration.py file in the version_id folder
# The new module should have an api.py file in the version_id folder
# A test file should be created in apps\connectors\tests\integrations

import argparse
import os


class Options:
    def __init__(self):
        self.id: str = ""
        self.name: str = ""
        self.category: str = ""
        self.version_id: str = ""
        self.vendor_id: str = ""
        self.supported_actions: list = []
        self.with_connection: bool = False
        parse_args(self)

    @property
    def integration_folder(self) -> str:
        return f"apps/connectors/integrations/vendors/{self.vendor_id}/{self.id}"

    @property
    def version_folder(self) -> str:
        return f"{self.integration_folder}/{self.version_id}"

    @property
    def actions_folder(self) -> str:
        return f"{self.version_folder}/actions"

    @property
    def demo_integration_folder(self) -> str:
        return f"apps/demo/integrations/{self.id}"

    @property
    def demo_version_folder(self) -> str:
        return f"{self.demo_integration_folder}/{self.version_id}"

    @property
    def version_name(self) -> str:
        # replace underscores with periods
        return self.version_id.replace("_", ".")

    @property
    def template_class_prefix(self) -> str:
        # camel case id
        return snake_to_camel(self.id)

    @property
    def version_class_prefix(self) -> str:
        # camel case id and version_id
        return f"{snake_to_camel(self.id)}{snake_to_camel(self.version_id)}"

    def __str__(self):
        return f"id: {self.id}, name: {self.name}, category: {self.category}, version_id: {self.version_id}, vendor_id: {self.vendor_id}, supported_actions: {self.supported_actions}"


def parse_args(options: Options):
    parser = argparse.ArgumentParser(
        description="Create a new integration from a template"
    )
    parser.add_argument("--id", type=str, help="integration id")
    parser.add_argument("--name", type=str, help="integration name")
    parser.add_argument("--version_id", type=str, help="integration version id")
    parser.add_argument("--vendor_id", type=str, help="integration vendor id")
    parser.add_argument("--with_connection", type=bool, help="with connection")
    return parser.parse_args(namespace=options)


def snake_to_camel(snake_str: str):
    components = snake_str.split("_")
    return "".join(x.title() for x in components)


def create_integration(options: Options):
    # Create the integration folder
    os.mkdir(options.integration_folder)

    # Create version folder
    os.mkdir(options.version_folder)

    # Create the actions folder in the version folder
    os.mkdir(options.actions_folder)

    # Create the __init__.py file
    with open(f"{options.integration_folder}/__init__.py", "w") as init_file:
        init_file.write(f"from .template import {snake_to_camel(options.id)}Template")

    # Create the actions __init__.py file
    with open(f"{options.actions_folder}/__init__.py", "w") as init_file:
        init_file.write("")


template_template = """
from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .{version_id} import {version_class_prefix}TemplateVersion


class {template_class_prefix}Template(Template):
    id = "{id}"
    name = "{name}"
    category = Template.Category.ENDPOINT_SECURITY
    versions = {{
        {version_class_prefix}TemplateVersion.id: {version_class_prefix}TemplateVersion(),
    }}
    vendor = Vendors.{vendor_key}
"""


def create_template(options: Options):
    # Create the template.py file
    with open(f"{options.integration_folder}/template.py", "w") as template_file:
        template_file.write(
            template_template.format(
                id=options.id,
                name=options.name,
                version_id=options.version_id,
                vendor_key=options.vendor_id.upper(),
                template_class_prefix=options.template_class_prefix,
                version_class_prefix=options.version_class_prefix,
            )
        )


host_sync_template = """
from typing import Generator

from apps.connectors.integrations.actions import normalize, normalize_last_seen, to_list
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSync,
    HostSyncArgs,
    AssetCriticality,
)

def normalize_host(host_data: dict):
    # Logics goes here
    return Host(
        source_id="",
        group_names=[],
        hostname="",
        fqdns=[],
        ip_addresses=[],
        mac_addresses=[],
        os="",  # OsAttributes
        owners=[],  # List[OwnerAttributes]
        aad_id=None,
        criticality=AssetCriticality.UNKNOWN,
        last_seen=normalize_last_seen(host_data.get("KEY_TO_LAST_SEEN")), # only works for iso date string or list of iso date strings
        source_data=host_data,
    )

class {version_class_prefix}HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        pass

    def get_permission_checks(self):
        pass
"""


def create_host_sync(options: Options):
    # Create the api.py file
    with open(f"{options.actions_folder}/host_sync.py", "w") as host_sync_file:
        host_sync_file.write(
            host_sync_template.format(
                version_class_prefix=options.version_class_prefix,
            )
        )


version_api_template = """
class {version_class_prefix}Api:
    def __init__(self, **kwargs):
        pass
"""


def create_version_api(options: Options):
    # Create the api.py file
    with open(f"{options.version_folder}/api.py", "w") as api_file:
        api_file.write(
            version_api_template.format(
                version_class_prefix=options.version_class_prefix,
            )
        )


version_config_template = """
from pydantic import  Field, HttpUrl

from apps.connectors.integrations import TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class {version_class_prefix}Config(TemplateVersionConfig):
    pass
"""

connection_template = """
from pydantic import Field, HttpUrl

from apps.connectors.integrations import TemplateVersionConfig
from apps.connectors.integrations.template import ConnectionTemplate
from apps.connectors.integrations.types import EncryptedStr


class {version_class_prefix}Config(TemplateVersionConfig):
    pass


class {version_class_prefix}Connection(ConnectionTemplate):
    id = "{id}"
    name = "{name}"
    config_model = {version_class_prefix}Config
"""


def create_version_config(options: Options):
    # Create the config.py file
    file_name = "connection.py" if options.with_connection else "config.py"
    with open(f"{options.version_folder}/{file_name}", "w") as template_file:
        string_template = (
            connection_template if options.with_connection else version_config_template
        )
        template_file.write(
            string_template.format(
                id=options.id,
                name=options.name,
                version_id=options.version_id,
                version_name=options.version_name,
                template_class_prefix=options.template_class_prefix,
                version_class_prefix=options.version_class_prefix,
            )
        )


version_health_check_template = """
from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
)
from apps.connectors.integrations.integration import IntegrationError

class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        api = self.integration.get_api()
        raise NotImplementedError()
"""


def create_version_health_check(options: Options):
    # Create the health_check.py file
    with open(f"{options.version_folder}/health_check.py", "w") as health_check_file:
        health_check_file.write(
            version_health_check_template.format(
                version_class_prefix=options.version_class_prefix,
            )
        )


version_integration_template = """
from apps.connectors.integrations import Integration

from .api import {version_class_prefix}Api
from .health_check import ConnectionHealthCheck

class {version_class_prefix}Integration(Integration):
    api_class = {version_class_prefix}Api
    actions = ()
    critical_health_checks = (ConnectionHealthCheck,)
"""


def create_version_integration(options: Options):
    # Create the integration.py file
    with open(f"{options.version_folder}/integration.py", "w") as integration_file:
        integration_file.write(
            version_integration_template.format(
                version_class_prefix=options.version_class_prefix,
            )
        )


version_settings_template = """
from apps.connectors.integrations import IntegrationActionType, create_settings_model
from apps.connectors.integrations.template import TemplateVersionActionSettings

{version_class_prefix}Settings = create_settings_model(
    "{version_class_prefix}Settings",
    {{
    }},
)
"""


def create_version_settings(options: Options):
    # Create the settings.py file
    with open(f"{options.version_folder}/settings.py", "w") as settings_file:
        settings_file.write(
            version_settings_template.format(
                version_class_prefix=options.version_class_prefix,
            )
        )


template_version_template_no_connection = """
from apps.connectors.integrations import TemplateVersion

from .config import {version_class_prefix}Config
from .integration import {version_class_prefix}Integration
from .settings import {version_class_prefix}Settings


class {version_class_prefix}TemplateVersion(TemplateVersion):
    integration = {version_class_prefix}Integration
    id = "{version_id}"
    name = "{version_name}"
    config_model = {version_class_prefix}Config
    settings_model = {version_class_prefix}Settings
"""


template_version_template_with_connection = """
from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.template import EmptyConfig

from .connection import {version_class_prefix}Connection
from .integration import {version_class_prefix}Integration
from .settings import {version_class_prefix}Settings


class {version_class_prefix}TemplateVersion(TemplateVersion):
    integration = {version_class_prefix}Integration
    id = "{version_id}"
    name = "{version_name}"
    connection_model = {version_class_prefix}Connection
    config_model = EmptyConfig
    settings_model = {version_class_prefix}Settings
"""


def create_template_version(options: Options):
    # Create the __init__.py file
    with open(f"{options.version_folder}/__init__.py", "w") as template_file:
        string_template = (
            template_version_template_with_connection
            if options.with_connection
            else template_version_template_no_connection
        )
        template_file.write(
            string_template.format(
                id=options.id,
                name=options.name,
                version_id=options.version_id,
                version_name=options.version_name,
                template_class_prefix=options.template_class_prefix,
                version_class_prefix=options.version_class_prefix,
            )
        )


integration_test_template = """
import responses

from apps.connectors.integrations import IntegrationActionType

from apps.connectors.tests.integrations.base import (
    BaseIntegrationTest,
    HealthCheckComponentTestMixin,
)
from apps.tests.base import BaseTestCase
from factories import ConnectorFactory

class {version_class_prefix}ApiTest(BaseTestCase):
    pass

class {version_class_prefix}IntegrationTest(BaseIntegrationTest):
    pass

class {version_class_prefix}HealthCheckTest(BaseTestCase):
    pass

class {version_class_prefix}HealthCheckComponentsTest(BaseTestCase, HealthCheckComponentTestMixin):
    pass
"""


def create_integration_test(options: Options):
    # Create the test file
    with open(
        f"apps/connectors/tests/integrations/{options.id}_{options.version_id}.py",
        "w",
    ) as test_file:
        test_file.write(
            integration_test_template.format(
                id=options.id,
                version_id=options.version_id,
                version_class_prefix=options.version_class_prefix,
            )
        )


def create_demo_integration(options: Options):
    # Create the integration folder
    os.mkdir(options.demo_integration_folder)

    # Create version folder
    os.mkdir(options.demo_version_folder)

    # Create the __init__.py file
    with open(f"{options.demo_integration_folder}/__init__.py", "w") as init_file:
        init_file.write(
            f"from .template import Demo{snake_to_camel(options.id)}Template"
        )


demo_template_template = """
from apps.connectors.integrations.{id} import {template_class_prefix}Template
from apps.demo.integrations.template import DemoTemplate

from .{version_id} import Demo{version_class_prefix}TemplateVersion


class Demo{template_class_prefix}Template(DemoTemplate):
    _replaces_template = {template_class_prefix}Template
    versions = {{
        Demo{version_class_prefix}TemplateVersion.id: Demo{version_class_prefix}TemplateVersion(),
    }}
"""


def create_demo_template(options: Options):
    # Create the template.py file
    with open(f"{options.demo_integration_folder}/template.py", "w") as template_file:
        template_file.write(
            demo_template_template.format(
                id=options.id,
                name=options.name,
                version_id=options.version_id,
                version_name=options.version_name,
                template_class_prefix=options.template_class_prefix,
                version_class_prefix=options.version_class_prefix,
            )
        )


demo_version_api_template = """
from apps.demo.integrations.config import DemoConfig

from .provider import {version_class_prefix}Provider

class Demo{version_class_prefix}Api:


    def __init__(self, organization_alias, technology_id, **kwargs):
        self.organization_alias = organization_alias
        self.technology_id = technology_id
"""


def create_demo_version_api(options: Options):
    # Create the api.py file
    with open(f"{options.demo_version_folder}/api.py", "w") as api_file:
        api_file.write(
            demo_version_api_template.format(
                version_class_prefix=options.version_class_prefix,
            )
        )


demo_version_integration_template = """
from apps.demo.integrations import DemoIntegration

from .api import Demo{version_class_prefix}Api
from .provider import {version_class_prefix}Provider


class Demo{version_class_prefix}Integration(DemoIntegration):
    api_class = Demo{version_class_prefix}Api
    provider = {version_class_prefix}Provider
"""


def create_demo_version_integration(options: Options):
    # Create the integration.py file
    with open(f"{options.demo_version_folder}/integration.py", "w") as integration_file:
        integration_file.write(
            demo_version_integration_template.format(
                version_class_prefix=options.version_class_prefix,
            )
        )


demo_version_provider_template = """
class {version_class_prefix}Provider:
    pass
"""


def create_demo_version_provider(options: Options):
    # Create the provider.py file
    with open(f"{options.demo_version_folder}/provider.py", "w") as provider_file:
        provider_file.write(
            demo_version_provider_template.format(
                version_class_prefix=options.version_class_prefix,
            )
        )


demo_template_version_template_no_connection = """
from apps.connectors.integrations import IntegrationActionType, TemplateVersion
from apps.connectors.integrations.{id}.{version_id} import {version_class_prefix}Settings
from apps.demo.integrations.template import DemoTemplateConfig

from .integration import Demo{version_class_prefix}Integration

class Demo{version_class_prefix}TemplateVersion(TemplateVersion):
    integration = Demo{version_class_prefix}Integration
    id = "{version_id}"
    name = "{version_name}"
    supported_actions = []
    settings_model = {version_class_prefix}Settings
    config_model = DemoTemplateConfig
"""


demo_template_version_template_with_connection = """
from apps.connectors.integrations import IntegrationActionType, TemplateVersion
from apps.connectors.integrations.{id}.{version_id} import {version_class_prefix}Settings
from apps.connectors.integrations.template import EmptyConfig
from apps.demo.integrations.template import DemoConnectionTemplate

from .integration import Demo{version_class_prefix}Integration

class Demo{version_class_prefix}TemplateVersion(TemplateVersion):
    integration = Demo{version_class_prefix}Integration
    id = "{version_id}"
    name = "{version_name}"
    supported_actions = []
    settings_model = {version_class_prefix}Settings
    connection_model = DemoConnectionTemplate
    config_model = EmptyConfig
"""


def create_demo_template_version(options: Options):
    # Create the __init__.py file
    with open(f"{options.demo_version_folder}/__init__.py", "w") as template_file:
        string_template = (
            demo_template_version_template_with_connection
            if options.with_connection
            else demo_template_version_template_no_connection
        )
        template_file.write(
            string_template.format(
                id=options.id,
                version_id=options.version_id,
                version_name=options.version_name,
                version_class_prefix=options.version_class_prefix,
            )
        )


demo_integration_test_template = """
from django.test import TestCase
from apps.connectors.integrations import IntegrationActionType, TemplateVersionSettings
from apps.demo.integrations.{id}.{version_id}.integration import Demo{version_class_prefix}Integration

class Demo{version_class_prefix}IntegrationTest(TestCase):
    pass
"""


def create_demo_integration_test(options: Options):
    # Create the test file
    with open(
        f"apps/demo/tests/integrations/{options.id}_{options.version_id}.py",
        "w",
    ) as test_file:
        test_file.write(
            demo_integration_test_template.format(
                id=options.id,
                version_id=options.version_id,
                version_class_prefix=options.version_class_prefix,
            )
        )


def new(options: Options):
    print(f"Creating new integration: {options}")
    create_integration(options)
    create_template(options)
    create_version_api(options)
    create_version_config(options)
    create_version_health_check(options)
    create_version_integration(options)
    create_version_settings(options)
    create_template_version(options)
    create_integration_test(options)
    create_host_sync(options)

    create_demo_integration(options)
    create_demo_template(options)
    create_demo_version_api(options)
    create_demo_version_integration(options)
    create_demo_version_provider(options)
    create_demo_template_version(options)
    create_demo_integration_test(options)


def main():
    options = Options()
    new(options)


if __name__ == "__main__":
    main()
