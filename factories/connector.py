import factory
from factory.django import DjangoModelFactory

from apps.connectors.integrations import Template
from apps.connectors.models import Connector
from factories import OrganizationFactory
from factories.connection import ConnectionFactory


class LazyParameterizedAttribute(factory.declarations.BaseDeclaration):
    def __init__(self, function):
        super().__init__()
        self.function = function

    def evaluate(self, instance, step, extra):
        lazy = self.function(instance)
        lazy.update(extra)
        return lazy


def lazy_parameterized(fun):
    return LazyParameterizedAttribute(fun)


def default_settings(technology_id, version_id):
    template_version = Template.get_template(technology_id).versions[version_id]
    return template_version.default_settings()


class ConnectorFactory(DjangoModelFactory):
    class Meta:
        model = Connector

    id = None
    technology_id = "azure_ad"
    version_id = "v1"
    connection = factory.SubFactory(ConnectionFactory)
    organization = factory.SubFactory(OrganizationFactory)
    enabled = True
    enabled_actions = []
    internal = False
    vulnerability_coverage_mode = "n/a"
    endpoint_coverage_mode = "n/a"

    @lazy_parameterized
    def config(self):
        return self.connection.config

    @lazy_parameterized
    def settings(self):
        return default_settings(self.technology_id, self.version_id)

    @classmethod
    def get_integration(cls, **kwargs):
        connector = cls.build(**kwargs)
        return connector.get_integration(decrypt_config=False)

    @classmethod
    def get_api(cls, **kwargs):
        integration = cls.get_integration(**kwargs)
        return integration.get_api()
