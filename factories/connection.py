import uuid
from enum import Enum

import factory
from factory import SelfAttribute, lazy_attribute
from factory.django import DjangoModelFactory
from pydantic import AnyUrl, BaseModel
from pydantic.fields import FieldInfo
from pydantic_core import PydanticUndefined, Url

from apps.connectors.integrations.template import ConnectionTemplate, Template
from apps.connectors.integrations.types import EncryptedStr
from apps.connectors.models.connection import Connection


def get_test_value(n: str, i: FieldInfo):
    if issubclass(i.annotation, Enum):
        return list(i.annotation)[0]
    if issubclass(i.annotation, uuid.UUID):
        return str(uuid.uuid4())
    if issubclass(i.annotation, float):
        return 0.0
    if n == "url" or issubclass(i.annotation, (Url, AnyUrl)):
        if i.default:
            if i.default != PydanticUndefined:
                return i.default
        return f"https://test_{n}.com"
    if issubclass(i.annotation, (str, EncryptedStr)):
        return f"test_{n}"
    raise ValueError(f"Unknown field type: {i.annotation}")  # pragma: no cover


class LazyParameterizedAttribute(factory.declarations.BaseDeclaration):
    def __init__(self, function):
        super().__init__()
        self.function = function

    def evaluate(self, instance, step, extra):
        lazy = self.function(instance)
        lazy.update(extra)
        return lazy


def lazy_parameterized(fun):
    return LazyParameterizedAttribute(fun)


def default_config(connection_template_id):
    if connection_template_id in ("demo_connection_template",):
        return {}
    try:
        connection_template = ConnectionTemplate.get_template(connection_template_id)
        config_model: BaseModel = connection_template.config_model
        config = {
            name: get_test_value(name, info)
            for name, info in config_model.model_fields.items()
        }
        return config
    except KeyError:  # pragma: no cover
        raise ValueError(f"Unknown connection_template_id: {connection_template_id}")


class ConnectionFactory(DjangoModelFactory):
    class Meta:
        model = Connection

    id = None
    # WARNING: "..organization" accesses the parent factory, so direct ConnectionFactory.build()
    # will not work, unless you specify organization in kwargs
    organization = SelfAttribute("..organization")

    @lazy_attribute
    def connection_template_id(self):
        technology_id = self.factory_parent.technology_id
        version_id = self.factory_parent.version_id

        return (
            Template.get_template(technology_id)
            .versions[version_id]
            .connection_model.id
        )

    @lazy_parameterized
    def config(self):
        return default_config(self.connection_template_id)
